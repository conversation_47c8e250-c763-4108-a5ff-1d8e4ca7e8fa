# 🚀 دليل النشر على Firebase

## المتطلبات الأساسية

1. **حساب Google/Firebase**
   - إنشاء حساب على [Firebase Console](https://console.firebase.google.com)
   
2. **Node.js و npm**
   - تأكد من تثبيت Node.js (الإصدار 16 أو أحدث)

## خطوات النشر التفصيلية

### 1️⃣ الإعداد الأولي

#### تسجيل الدخول إلى Firebase
```bash
npm run firebase:login
```
أو
```bash
firebase login
```

#### إنشاء مشروع Firebase جديد
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. اضغط "Add project" أو "إضافة مشروع"
3. أدخل اسم المشروع: `alqaisar-contract-system`
4. اختر الإعدادات المناسبة
5. انتظر حتى يتم إنشاء المشروع

#### ربط المشروع المحلي بـ Firebase
```bash
npm run firebase:init
```

**اختر الإعدادات التالية:**
- ✅ Hosting: Configure files for Firebase Hosting
- ✅ Use an existing project
- ✅ اختر `alqaisar-contract-system`
- ✅ Public directory: `dist`
- ✅ Configure as single-page app: `Yes`
- ✅ Set up automatic builds: `No`
- ✅ File dist/index.html already exists. Overwrite: `No`

### 2️⃣ بناء ونشر المشروع

#### الطريقة الأولى: استخدام السكريبت المدمج
```bash
npm run deploy
```

#### الطريقة الثانية: خطوة بخطوة
```bash
# بناء المشروع
npm run build

# نشر على Firebase
firebase deploy
```

#### الطريقة الثالثة: استخدام ملف bat (Windows)
```bash
./deploy.bat
```

### 3️⃣ التحقق من النشر

بعد النشر الناجح، ستحصل على رابط مثل:
```
✔ Deploy complete!

Project Console: https://console.firebase.google.com/project/alqaisar-contract-system/overview
Hosting URL: https://alqaisar-contract-system.web.app
```

## 🔧 إعدادات متقدمة

### تخصيص النطاق (Domain)
1. اذهب إلى Firebase Console
2. اختر مشروعك
3. اذهب إلى Hosting
4. اضغط "Add custom domain"
5. اتبع التعليمات لربط نطاقك

### إعدادات الأمان
```json
// في firebase.json
{
  "hosting": {
    "headers": [
      {
        "source": "**",
        "headers": [
          {
            "key": "X-Frame-Options",
            "value": "DENY"
          },
          {
            "key": "X-Content-Type-Options",
            "value": "nosniff"
          }
        ]
      }
    ]
  }
}
```

## 🔄 التحديثات والصيانة

### تحديث الموقع
```bash
# بعد إجراء تغييرات على الكود
npm run deploy
```

### عرض سجل النشر
```bash
firebase hosting:releases
```

### التراجع عن نشر
```bash
firebase hosting:rollback
```

## 🐛 حل المشاكل الشائعة

### مشكلة: "Firebase command not found"
```bash
npm install -g firebase-tools
```

### مشكلة: "Permission denied"
```bash
firebase logout
firebase login
```

### مشكلة: "Build failed"
```bash
# تنظيف وإعادة تثبيت
rm -rf node_modules
rm package-lock.json
npm install
npm run build
```

### مشكلة: "404 Page not found"
تأكد من إعداد `rewrites` في `firebase.json`:
```json
{
  "hosting": {
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ]
  }
}
```

## 📊 مراقبة الأداء

### عرض إحصائيات الموقع
1. اذهب إلى Firebase Console
2. اختر مشروعك
3. اذهب إلى Hosting
4. اعرض Analytics

### تحسين الأداء
- تفعيل ضغط Gzip
- تحسين الصور
- استخدام CDN

## 🔐 الأمان والنسخ الاحتياطي

### نسخ احتياطي من الإعدادات
```bash
# تصدير إعدادات Firebase
firebase use --add
```

### حماية الموقع
- تفعيل HTTPS (تلقائي في Firebase)
- إعداد قواعد الأمان
- مراقبة الوصول

## 📞 الدعم

للحصول على المساعدة:
- [وثائق Firebase](https://firebase.google.com/docs/hosting)
- [مجتمع Firebase](https://firebase.google.com/community)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/firebase-hosting)

---

**ملاحظة:** تأكد من الاحتفاظ بنسخة احتياطية من ملفات التكوين قبل إجراء أي تغييرات.
