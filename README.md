# نظام إدخال بيانات العقود - القيصر

## 📋 وصف المشروع

نظام إدخال وإدارة بيانات عقود الموظفين مصمم خصيص<|im_start|> لمحافظة الديوانية. يتضمن النظام واجهة عربية حديثة وسهلة الاستخدام لإدخال وعرض وتصدير بيانات الموظفين.

## ✨ الميزات

- 🏠 **لوحة تحكم رئيسية** مع بطاقات تفاعلية ملونة
- 📝 **إدخال بيانات الموظفين** مع التحقق من صحة البيانات
- 📊 **تقارير وإحصائيات** مع إمكانية التصدير
- ⚙️ **إعدادات النظام** وإدارة المستخدمين
- 🧭 **تنقل سهل** عبر شريط التنقل السفلي
- 📱 **تصميم متجاوب** يعمل على جميع الأجهزة
- 🎨 **واجهة عربية جميلة** بألوان متدرجة

## 🛠️ التقنيات المستخدمة

- **React 18** - مكتبة واجهة المستخدم
- **Vite** - أداة البناء السريعة
- **CSS3** - التصميم والتنسيق
- **Firebase Hosting** - استضافة الويب

## 🚀 التثبيت والتشغيل

### متطلبات النظام
- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd alqaisar-contract-system
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **تشغيل المشروع محلي<|im_start|>**
```bash
npm run dev
```

4. **فتح المتصفح على**
```
http://localhost:3000
```

## 🔥 النشر على Firebase

### الإعداد الأولي

1. **تسجيل الدخول إلى Firebase**
```bash
firebase login
```

2. **إنشاء مشروع Firebase جديد**
```bash
firebase init hosting
```

3. **اختيار الإعدادات التالية:**
   - Public directory: `dist`
   - Single-page app: `Yes`
   - Automatic builds: `No`

### بناء ونشر المشروع

1. **بناء المشروع للإنتاج**
```bash
npm run build
```

2. **نشر على Firebase**
```bash
firebase deploy
```

3. **عرض الموقع**
```bash
firebase open hosting:site
```

## 📁 هيكل المشروع

```
alqaisar-contract-system/
├── public/
├── src/
│   ├── App.jsx              # المكون الرئيسي
│   ├── main.jsx            # نقطة دخول التطبيق
│   └── styles/
│       └── global.css      # الأنماط العامة
├── firebase.json           # إعدادات Firebase
├── .firebaserc            # مشروع Firebase
├── vite.config.js         # إعدادات Vite
└── package.json           # تبعيات المشروع
```

## 🎯 كيفية الاستخدام

### إدخال بيانات موظف جديد
1. انقر على بطاقة "البيانات" في الصفحة الرئيسية
2. املأ جميع الحقول المطلوبة (الاسم الكامل واسم الأم إجباريان)
3. اضغط "إضافة موظف"

### عرض التقارير
1. انقر على بطاقة "التقارير"
2. اعرض جدول البيانات
3. اضغط "تصدير CSV" لتحميل التقرير

### الإعدادات
1. انقر على بطاقة "الإعدادات"
2. اعرض معلومات المستخدم والنظام

## 🔧 التخصيص

### تغيير الألوان
عدل الألوان في ملف `src/styles/global.css`:

```css
/* ألوان البطاقات */
.card-data {
  --card-color-1: #667eea;
  --card-color-2: #764ba2;
}
```

### إضافة حقول جديدة
عدل في `src/App.jsx` في مكون `DataEntryPage`:

```jsx
const [formData, setFormData] = useState({
  // أضف الحقول الجديدة هنا
  newField: ''
});
```

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- التواصل مع فريق التطوير

## 📄 الترخيص

هذا المشروع مخصص لمحافظة الديوانية - العراق

---

**تم التطوير بواسطة:** فريق تطوير نظم المعلومات  
**التاريخ:** 2024  
**الإصدار:** 1.0.0
