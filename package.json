{"name": "alqaisar-contract-system", "private": true, "version": "1.0.0", "type": "module", "description": "نظام إدخال بيانات العقود - محافظة الديوانية", "author": "فريق تطوير نظم المعلومات", "keywords": ["react", "firebase", "contracts", "employees", "arabic"], "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "deploy": "npm run build && firebase deploy", "firebase:init": "firebase init hosting", "firebase:login": "firebase login", "check": "node check-deployment.js", "predeploy": "npm run check"}, "dependencies": {"firebase": "^10.14.1", "react": "^18.2.0", "react-dom": "^18.2.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-react": "^4.2.1", "firebase-tools": "^14.5.1", "terser": "^5.40.0", "vite": "^5.0.8"}}