import React, { createContext, useContext, useReducer, useEffect } from 'react';

const AppContext = createContext();

const initialState = {
  employees: [],
  currentUser: {
    name: 'علي عاجل خشان المحنة',
    role: 'admin', // admin or user
    governorate: 'محافظة الديوانية'
  },
  settings: {
    theme: 'light',
    language: 'ar'
  },
  loading: false,
  error: null
};

function appReducer(state, action) {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    case 'ADD_EMPLOYEE':
      const newEmployee = {
        ...action.payload,
        id: Date.now().toString(),
        createdAt: new Date().toISOString()
      };
      const updatedEmployees = [...state.employees, newEmployee];
      localStorage.setItem('employees', JSON.stringify(updatedEmployees));
      return { ...state, employees: updatedEmployees };
    
    case 'UPDATE_EMPLOYEE':
      const updated = state.employees.map(emp => 
        emp.id === action.payload.id ? { ...emp, ...action.payload } : emp
      );
      localStorage.setItem('employees', JSON.stringify(updated));
      return { ...state, employees: updated };
    
    case 'DELETE_EMPLOYEE':
      const filtered = state.employees.filter(emp => emp.id !== action.payload);
      localStorage.setItem('employees', JSON.stringify(filtered));
      return { ...state, employees: filtered };
    
    case 'LOAD_EMPLOYEES':
      return { ...state, employees: action.payload };
    
    case 'UPDATE_SETTINGS':
      const newSettings = { ...state.settings, ...action.payload };
      localStorage.setItem('settings', JSON.stringify(newSettings));
      return { ...state, settings: newSettings };
    
    case 'CLEAR_ERROR':
      return { ...state, error: null };
    
    default:
      return state;
  }
}

export function AppProvider({ children }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Load data from localStorage on mount
  useEffect(() => {
    const savedEmployees = localStorage.getItem('employees');
    const savedSettings = localStorage.getItem('settings');
    
    if (savedEmployees) {
      dispatch({ type: 'LOAD_EMPLOYEES', payload: JSON.parse(savedEmployees) });
    }
    
    if (savedSettings) {
      dispatch({ type: 'UPDATE_SETTINGS', payload: JSON.parse(savedSettings) });
    }
  }, []);

  const addEmployee = (employeeData) => {
    dispatch({ type: 'ADD_EMPLOYEE', payload: employeeData });
  };

  const updateEmployee = (id, employeeData) => {
    dispatch({ type: 'UPDATE_EMPLOYEE', payload: { id, ...employeeData } });
  };

  const deleteEmployee = (id) => {
    dispatch({ type: 'DELETE_EMPLOYEE', payload: id });
  };

  const updateSettings = (settings) => {
    dispatch({ type: 'UPDATE_SETTINGS', payload: settings });
  };

  const setLoading = (loading) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  };

  const setError = (error) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const value = {
    ...state,
    addEmployee,
    updateEmployee,
    deleteEmployee,
    updateSettings,
    setLoading,
    setError,
    clearError
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}
