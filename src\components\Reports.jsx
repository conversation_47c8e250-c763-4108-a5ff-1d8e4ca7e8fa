import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  FaChartBar, 
  FaFileExcel, 
  FaEye, 
  FaSearch,
  FaCalendarAlt,
  FaUsers,
  FaArrowRight
} from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import { useNavigate } from 'react-router-dom';
import * as XLSX from 'xlsx';

const Reports = () => {
  const { employees } = useApp();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [showPreview, setShowPreview] = useState(false);

  // Filter employees based on search and date
  const filteredEmployees = employees.filter(emp => {
    const matchesSearch = emp.fullName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         emp.nationalId?.includes(searchTerm) ||
                         emp.phoneNumber?.includes(searchTerm);
    
    const matchesDate = !dateFilter || 
                       new Date(emp.createdAt).toDateString() === new Date(dateFilter).toDateString();
    
    return matchesSearch && matchesDate;
  });

  const exportToExcel = () => {
    const exportData = filteredEmployees.map((emp, index) => ({
      'الرقم': index + 1,
      'الاسم الكامل': emp.fullName,
      'اسم الأم': emp.motherName,
      'تاريخ الميلاد': new Date(emp.birthDate).toLocaleDateString('ar-SA'),
      'الرقم الوطني': emp.nationalId,
      'رقم الهاتف': emp.phoneNumber,
      'رقم أمر النقل': emp.transferOrderNumber,
      'تاريخ بداية الأمر': new Date(emp.orderStartDate).toLocaleDateString('ar-SA'),
      'الدائرة المعاد تدويرها': emp.recycledCircle,
      'التخصص': emp.specialization,
      'تاريخ الإدخال': new Date(emp.createdAt).toLocaleDateString('ar-SA')
    }));

    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'تقرير الموظفين');
    
    // Set column widths
    const colWidths = [
      { wch: 8 },  // الرقم
      { wch: 20 }, // الاسم الكامل
      { wch: 20 }, // اسم الأم
      { wch: 15 }, // تاريخ الميلاد
      { wch: 15 }, // الرقم الوطني
      { wch: 15 }, // رقم الهاتف
      { wch: 15 }, // رقم أمر النقل
      { wch: 15 }, // تاريخ بداية الأمر
      { wch: 25 }, // الدائرة المعاد تدويرها
      { wch: 15 }, // التخصص
      { wch: 15 }  // تاريخ الإدخال
    ];
    ws['!cols'] = colWidths;

    XLSX.writeFile(wb, `تقرير_الموظفين_${new Date().toLocaleDateString('ar-SA')}.xlsx`);
  };

  const generateSummaryReport = () => {
    const summary = {
      totalEmployees: employees.length,
      todayEntries: employees.filter(emp => {
        const today = new Date().toDateString();
        const empDate = new Date(emp.createdAt).toDateString();
        return today === empDate;
      }).length,
      weekEntries: employees.filter(emp => {
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return new Date(emp.createdAt) >= weekAgo;
      }).length,
      specializations: {}
    };

    // Count specializations
    employees.forEach(emp => {
      if (emp.specialization) {
        summary.specializations[emp.specialization] = 
          (summary.specializations[emp.specialization] || 0) + 1;
      }
    });

    return summary;
  };

  const summary = generateSummaryReport();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <div className="container">
      {/* Header */}
      <motion.div 
        className="header"
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <h1>
          <FaChartBar style={{ marginLeft: '10px' }} />
          التقارير والإحصائيات
        </h1>
        <div className="subtitle">
          عرض وتصدير تقارير بيانات الموظفين
        </div>
      </motion.div>

      {/* Summary Cards */}
      <motion.div 
        className="dashboard-grid"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        style={{ marginBottom: '30px' }}
      >
        <motion.div className="card" variants={itemVariants} style={{ textAlign: 'center' }}>
          <FaUsers style={{ fontSize: '2rem', color: '#667eea', marginBottom: '10px' }} />
          <h3 style={{ color: '#333', marginBottom: '5px' }}>إجمالي الموظفين</h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#667eea' }}>
            {summary.totalEmployees}
          </div>
        </motion.div>

        <motion.div className="card" variants={itemVariants} style={{ textAlign: 'center' }}>
          <FaCalendarAlt style={{ fontSize: '2rem', color: '#f5576c', marginBottom: '10px' }} />
          <h3 style={{ color: '#333', marginBottom: '5px' }}>إدخالات اليوم</h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#f5576c' }}>
            {summary.todayEntries}
          </div>
        </motion.div>

        <motion.div className="card" variants={itemVariants} style={{ textAlign: 'center' }}>
          <FaChartBar style={{ fontSize: '2rem', color: '#38f9d7', marginBottom: '10px' }} />
          <h3 style={{ color: '#333', marginBottom: '5px' }}>هذا الأسبوع</h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#38f9d7' }}>
            {summary.weekEntries}
          </div>
        </motion.div>
      </motion.div>

      {/* Action Buttons */}
      <motion.div 
        style={{ 
          display: 'flex', 
          gap: '15px', 
          marginBottom: '30px',
          flexWrap: 'wrap',
          justifyContent: 'center'
        }}
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.button 
          className="btn btn-success"
          onClick={exportToExcel}
          variants={itemVariants}
        >
          <FaFileExcel style={{ marginLeft: '5px' }} />
          تصدير Excel
        </motion.button>

        <motion.button 
          className="btn btn-primary"
          onClick={() => setShowPreview(!showPreview)}
          variants={itemVariants}
        >
          <FaEye style={{ marginLeft: '5px' }} />
          {showPreview ? 'إخفاء المعاينة' : 'معاينة التقرير'}
        </motion.button>

        <motion.button 
          className="btn btn-secondary"
          onClick={() => navigate('/')}
          variants={itemVariants}
        >
          <FaArrowRight style={{ marginLeft: '5px' }} />
          العودة للرئيسية
        </motion.button>
      </motion.div>

      {/* Search and Filter */}
      <motion.div 
        className="form-container"
        style={{ marginBottom: '20px' }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <div style={{ display: 'flex', gap: '15px', flexWrap: 'wrap' }}>
          <div style={{ flex: 1, minWidth: '200px' }}>
            <label>
              <FaSearch style={{ marginLeft: '5px' }} />
              البحث
            </label>
            <input
              type="text"
              placeholder="ابحث بالاسم أو الرقم الوطني أو الهاتف..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div style={{ flex: 1, minWidth: '200px' }}>
            <label>
              <FaCalendarAlt style={{ marginLeft: '5px' }} />
              تصفية بالتاريخ
            </label>
            <input
              type="date"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
            />
          </div>
        </div>
      </motion.div>

      {/* Data Table */}
      {showPreview && (
        <motion.div 
          className="table-container"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <h3 style={{ marginBottom: '20px', color: '#333' }}>
            معاينة البيانات ({filteredEmployees.length} موظف)
          </h3>
          
          {filteredEmployees.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
              <FaUsers style={{ fontSize: '3rem', marginBottom: '15px', opacity: 0.3 }} />
              <p>لا توجد بيانات للعرض</p>
            </div>
          ) : (
            <div style={{ overflowX: 'auto' }}>
              <table className="table">
                <thead>
                  <tr>
                    <th>الرقم</th>
                    <th>الاسم الكامل</th>
                    <th>تاريخ الميلاد</th>
                    <th>رقم الهاتف</th>
                    <th>رقم أمر النقل</th>
                    <th>التخصص</th>
                    <th>تاريخ الإدخال</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredEmployees.map((employee, index) => (
                    <motion.tr 
                      key={employee.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                    >
                      <td>{index + 1}</td>
                      <td>{employee.fullName}</td>
                      <td>{new Date(employee.birthDate).toLocaleDateString('ar-SA')}</td>
                      <td>{employee.phoneNumber}</td>
                      <td>{employee.transferOrderNumber}</td>
                      <td>{employee.specialization}</td>
                      <td>{new Date(employee.createdAt).toLocaleDateString('ar-SA')}</td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </motion.div>
      )}

      {/* Specializations Summary */}
      {Object.keys(summary.specializations).length > 0 && (
        <motion.div 
          className="form-container"
          style={{ marginTop: '30px' }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <h3 style={{ marginBottom: '20px', color: '#333' }}>توزيع التخصصات</h3>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
            {Object.entries(summary.specializations).map(([spec, count]) => (
              <div key={spec} className="card" style={{ textAlign: 'center', padding: '15px' }}>
                <h4 style={{ color: '#667eea', marginBottom: '5px' }}>{spec}</h4>
                <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#333' }}>
                  {count}
                </div>
                <p style={{ color: '#666', fontSize: '0.9rem' }}>موظف</p>
              </div>
            ))}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default Reports;
