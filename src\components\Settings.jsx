import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON>aCog, 
  Fa<PERSON>ser, 
  FaPalette, 
  FaArchive,
  FaShieldAlt,
  FaArrowRight,
  FaSave,
  FaMoon,
  FaSun,
  FaLanguage
} from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import { useNavigate } from 'react-router-dom';

const Settings = () => {
  const { currentUser, settings, updateSettings } = useApp();
  const navigate = useNavigate();
  const [localSettings, setLocalSettings] = useState(settings);
  const [showSaveMessage, setShowSaveMessage] = useState(false);

  const handleSaveSettings = () => {
    updateSettings(localSettings);
    setShowSaveMessage(true);
    setTimeout(() => setShowSaveMessage(false), 3000);
  };

  const handleSettingChange = (key, value) => {
    setLocalSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <div className="container">
      {/* Header */}
      <motion.div 
        className="header"
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <h1>
          <FaCog style={{ marginLeft: '10px' }} />
          الإعدادات
        </h1>
        <div className="subtitle">
          تخصيص إعدادات النظام والمظهر
        </div>
      </motion.div>

      {/* Success Message */}
      {showSaveMessage && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          className="card"
          style={{
            background: 'linear-gradient(135deg, #43e97b, #38f9d7)',
            color: 'white',
            textAlign: 'center',
            padding: '15px',
            marginBottom: '20px'
          }}
        >
          <h4>✅ تم حفظ الإعدادات بنجاح!</h4>
        </motion.div>
      )}

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* User Information */}
        <motion.div className="form-container" variants={itemVariants}>
          <h3 style={{ marginBottom: '20px', color: '#333', display: 'flex', alignItems: 'center' }}>
            <FaUser style={{ marginLeft: '10px' }} />
            معلومات المستخدم
          </h3>
          
          <div className="form-group">
            <label>اسم المستخدم</label>
            <input
              type="text"
              value={currentUser.name}
              disabled
              style={{ backgroundColor: '#f8f9fa', cursor: 'not-allowed' }}
            />
          </div>

          <div className="form-group">
            <label>الدور</label>
            <input
              type="text"
              value={currentUser.role === 'admin' ? 'مدير النظام' : 'مستخدم'}
              disabled
              style={{ backgroundColor: '#f8f9fa', cursor: 'not-allowed' }}
            />
          </div>

          <div className="form-group">
            <label>المحافظة</label>
            <input
              type="text"
              value={currentUser.governorate}
              disabled
              style={{ backgroundColor: '#f8f9fa', cursor: 'not-allowed' }}
            />
          </div>
        </motion.div>

        {/* Appearance Settings */}
        <motion.div className="form-container" variants={itemVariants}>
          <h3 style={{ marginBottom: '20px', color: '#333', display: 'flex', alignItems: 'center' }}>
            <FaPalette style={{ marginLeft: '10px' }} />
            إعدادات المظهر
          </h3>

          <div className="form-group">
            <label>
              <FaMoon style={{ marginLeft: '5px' }} />
              المظهر
            </label>
            <select
              value={localSettings.theme}
              onChange={(e) => handleSettingChange('theme', e.target.value)}
            >
              <option value="light">فاتح</option>
              <option value="dark">داكن</option>
              <option value="auto">تلقائي</option>
            </select>
          </div>

          <div className="form-group">
            <label>
              <FaLanguage style={{ marginLeft: '5px' }} />
              اللغة
            </label>
            <select
              value={localSettings.language}
              onChange={(e) => handleSettingChange('language', e.target.value)}
            >
              <option value="ar">العربية</option>
              <option value="en">English</option>
            </select>
          </div>

          {/* Color Scheme Preview */}
          <div style={{ marginTop: '20px' }}>
            <label style={{ marginBottom: '10px', display: 'block' }}>معاينة الألوان</label>
            <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
              <div style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                background: 'linear-gradient(135deg, #667eea, #764ba2)',
                border: '3px solid white',
                boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
              }}></div>
              <div style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                background: 'linear-gradient(135deg, #f093fb, #f5576c)',
                border: '3px solid white',
                boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
              }}></div>
              <div style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                background: 'linear-gradient(135deg, #4facfe, #00f2fe)',
                border: '3px solid white',
                boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
              }}></div>
              <div style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                background: 'linear-gradient(135deg, #43e97b, #38f9d7)',
                border: '3px solid white',
                boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
              }}></div>
            </div>
          </div>
        </motion.div>

        {/* System Settings */}
        {currentUser.role === 'admin' && (
          <motion.div className="form-container" variants={itemVariants}>
            <h3 style={{ marginBottom: '20px', color: '#333', display: 'flex', alignItems: 'center' }}>
              <FaShieldAlt style={{ marginLeft: '10px' }} />
              إعدادات النظام (المدير فقط)
            </h3>

            <div className="form-group">
              <label>صلاحيات المستخدمين</label>
              <select>
                <option value="standard">قياسية</option>
                <option value="advanced">متقدمة</option>
                <option value="admin">مدير</option>
              </select>
            </div>

            <div className="form-group">
              <label>نسخ احتياطي تلقائي</label>
              <select>
                <option value="daily">يومي</option>
                <option value="weekly">أسبوعي</option>
                <option value="monthly">شهري</option>
                <option value="disabled">معطل</option>
              </select>
            </div>

            <div style={{ 
              background: '#f8f9fa', 
              padding: '15px', 
              borderRadius: '10px',
              border: '1px solid #e9ecef'
            }}>
              <h4 style={{ color: '#495057', marginBottom: '10px' }}>الوصول السريع</h4>
              <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                <button 
                  className="btn btn-secondary"
                  onClick={() => navigate('/archive')}
                  style={{ fontSize: '0.9rem' }}
                >
                  <FaArchive style={{ marginLeft: '5px' }} />
                  الأرشيف العام
                </button>
                <button 
                  className="btn btn-secondary"
                  style={{ fontSize: '0.9rem' }}
                  onClick={() => alert('ميزة إدارة المستخدمين قيد التطوير')}
                >
                  <FaUser style={{ marginLeft: '5px' }} />
                  إدارة المستخدمين
                </button>
              </div>
            </div>
          </motion.div>
        )}

        {/* Quick Actions */}
        <motion.div className="form-container" variants={itemVariants}>
          <h3 style={{ marginBottom: '20px', color: '#333' }}>إجراءات سريعة</h3>
          
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
            <div className="card" style={{ textAlign: 'center', padding: '20px', cursor: 'pointer' }}
                 onClick={() => navigate('/data-entry')}>
              <FaUser style={{ fontSize: '2rem', color: '#667eea', marginBottom: '10px' }} />
              <h4>إضافة موظف جديد</h4>
            </div>

            <div className="card" style={{ textAlign: 'center', padding: '20px', cursor: 'pointer' }}
                 onClick={() => navigate('/reports')}>
              <FaArchive style={{ fontSize: '2rem', color: '#f5576c', marginBottom: '10px' }} />
              <h4>عرض التقارير</h4>
            </div>

            <div className="card" style={{ textAlign: 'center', padding: '20px', cursor: 'pointer' }}
                 onClick={() => {
                   const data = JSON.stringify({ settings: localSettings, user: currentUser }, null, 2);
                   const blob = new Blob([data], { type: 'application/json' });
                   const url = URL.createObjectURL(blob);
                   const a = document.createElement('a');
                   a.href = url;
                   a.download = 'backup.json';
                   a.click();
                 }}>
              <FaSave style={{ fontSize: '2rem', color: '#38f9d7', marginBottom: '10px' }} />
              <h4>نسخ احتياطي</h4>
            </div>
          </div>
        </motion.div>

        {/* Action Buttons */}
        <motion.div 
          style={{ 
            display: 'flex', 
            gap: '15px', 
            justifyContent: 'center',
            marginTop: '30px'
          }}
          variants={itemVariants}
        >
          <button 
            className="btn btn-primary"
            onClick={handleSaveSettings}
          >
            <FaSave style={{ marginLeft: '5px' }} />
            حفظ الإعدادات
          </button>
          
          <button 
            className="btn btn-secondary"
            onClick={() => navigate('/')}
          >
            <FaArrowRight style={{ marginLeft: '5px' }} />
            العودة للرئيسية
          </button>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default Settings;
