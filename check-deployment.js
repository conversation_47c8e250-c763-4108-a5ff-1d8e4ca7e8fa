#!/usr/bin/env node

/**
 * سكريبت فحص جاهزية المشروع للنشر
 * يتحقق من جميع الملفات والإعدادات المطلوبة
 */

import fs from 'fs';
import path from 'path';

const requiredFiles = [
  'package.json',
  'firebase.json',
  '.firebaserc',
  'vite.config.js',
  'src/App.jsx',
  'src/main.jsx',
  'src/styles/global.css',
  'index.html'
];

const requiredDirs = [
  'src',
  'src/styles'
];

function checkFile(filePath) {
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${filePath}`);
    return true;
  } else {
    console.log(`❌ ${filePath} - مفقود`);
    return false;
  }
}

function checkDirectory(dirPath) {
  if (fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()) {
    console.log(`✅ ${dirPath}/`);
    return true;
  } else {
    console.log(`❌ ${dirPath}/ - مفقود`);
    return false;
  }
}

function checkPackageJson() {
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const requiredScripts = ['dev', 'build', 'deploy'];
    const requiredDeps = ['react', 'react-dom'];
    
    console.log('\n📦 فحص package.json:');
    
    let allGood = true;
    
    // فحص السكريبتات
    requiredScripts.forEach(script => {
      if (packageJson.scripts && packageJson.scripts[script]) {
        console.log(`✅ Script: ${script}`);
      } else {
        console.log(`❌ Script: ${script} - مفقود`);
        allGood = false;
      }
    });
    
    // فحص التبعيات
    requiredDeps.forEach(dep => {
      if (packageJson.dependencies && packageJson.dependencies[dep]) {
        console.log(`✅ Dependency: ${dep}`);
      } else {
        console.log(`❌ Dependency: ${dep} - مفقود`);
        allGood = false;
      }
    });
    
    return allGood;
  } catch (error) {
    console.log('❌ خطأ في قراءة package.json:', error.message);
    return false;
  }
}

function checkFirebaseConfig() {
  try {
    const firebaseJson = JSON.parse(fs.readFileSync('firebase.json', 'utf8'));
    
    console.log('\n🔥 فحص إعدادات Firebase:');
    
    if (firebaseJson.hosting) {
      console.log('✅ إعدادات Hosting موجودة');
      
      if (firebaseJson.hosting.public === 'dist') {
        console.log('✅ مجلد النشر: dist');
      } else {
        console.log('❌ مجلد النشر يجب أن يكون: dist');
        return false;
      }
      
      if (firebaseJson.hosting.rewrites) {
        console.log('✅ إعدادات Rewrites موجودة');
      } else {
        console.log('❌ إعدادات Rewrites مفقودة');
        return false;
      }
      
      return true;
    } else {
      console.log('❌ إعدادات Hosting مفقودة');
      return false;
    }
  } catch (error) {
    console.log('❌ خطأ في قراءة firebase.json:', error.message);
    return false;
  }
}

function checkBuildOutput() {
  console.log('\n🏗️ فحص مخرجات البناء:');
  
  if (fs.existsSync('dist')) {
    console.log('✅ مجلد dist موجود');
    
    const distFiles = ['index.html'];
    let allGood = true;
    
    distFiles.forEach(file => {
      const filePath = path.join('dist', file);
      if (fs.existsSync(filePath)) {
        console.log(`✅ dist/${file}`);
      } else {
        console.log(`❌ dist/${file} - مفقود`);
        allGood = false;
      }
    });
    
    return allGood;
  } else {
    console.log('❌ مجلد dist مفقود - قم بتشغيل npm run build');
    return false;
  }
}

function main() {
  console.log('🔍 فحص جاهزية المشروع للنشر على Firebase\n');
  
  let allChecksPass = true;
  
  // فحص الملفات المطلوبة
  console.log('📁 فحص الملفات المطلوبة:');
  requiredFiles.forEach(file => {
    if (!checkFile(file)) {
      allChecksPass = false;
    }
  });
  
  // فحص المجلدات المطلوبة
  console.log('\n📂 فحص المجلدات المطلوبة:');
  requiredDirs.forEach(dir => {
    if (!checkDirectory(dir)) {
      allChecksPass = false;
    }
  });
  
  // فحص package.json
  if (!checkPackageJson()) {
    allChecksPass = false;
  }
  
  // فحص إعدادات Firebase
  if (!checkFirebaseConfig()) {
    allChecksPass = false;
  }
  
  // فحص مخرجات البناء
  if (!checkBuildOutput()) {
    allChecksPass = false;
  }
  
  // النتيجة النهائية
  console.log('\n' + '='.repeat(50));
  if (allChecksPass) {
    console.log('🎉 المشروع جاهز للنشر على Firebase!');
    console.log('\nيمكنك الآن تشغيل:');
    console.log('npm run deploy');
  } else {
    console.log('⚠️ المشروع غير جاهز للنشر');
    console.log('يرجى إصلاح المشاكل المذكورة أعلاه');
  }
  console.log('='.repeat(50));
  
  process.exit(allChecksPass ? 0 : 1);
}

main();
