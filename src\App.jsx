import React, { useState, useEffect } from 'react';
import {
  addEmployee as addEmployeeToFirebase,
  getAllEmployees,
  subscribeToEmployees,
  testConnection
} from './firebase/employeeService.js';
import * as XLSX from 'xlsx';

// دالة تحويل الأرقام من الإنجليزية إلى العربية
const convertToArabicNumbers = (text) => {
  if (!text) return text;
  const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
  const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];

  let result = text.toString();
  for (let i = 0; i < englishNumbers.length; i++) {
    result = result.replace(new RegExp(englishNumbers[i], 'g'), arabicNumbers[i]);
  }
  return result;
};

// دالة التنسيق المتقدم مع Header
function applyAdvancedProfessionalFormatting(ws, dataLength, headerRows = 6) {
  const range = XLSX.utils.decode_range(ws['!ref']);

  // تعيين ارتفاع الصفوف
  ws['!rows'] = [];
  for (let i = 0; i < headerRows; i++) {
    ws['!rows'][i] = { hpt: 25 }; // ارتفاع صفوف الـ Header
  }
  ws['!rows'][headerRows] = { hpt: 35 }; // ارتفاع صف العناوين

  // تعيين عرض الأعمدة بشكل احترافي
  ws['!cols'] = [
    { wch: 8 },   // الرقم
    { wch: 25 },  // الاسم الكامل
    { wch: 25 },  // اسم الأم
    { wch: 15 },  // تاريخ الميلاد
    { wch: 18 },  // الرقم الوطني
    { wch: 15 },  // رقم الهاتف
    { wch: 18 },  // رقم امر المباشرة
    { wch: 18 },  // تاريخ امر المباشرة
    { wch: 30 },  // الدائرة
    { wch: 25 },  // التخصص
    { wch: 15 }   // تاريخ الإدخال
  ];

  // تطبيق التنسيق على كل خلية
  for (let R = range.s.r; R <= range.e.r; ++R) {
    for (let C = range.s.c; C <= range.e.c; ++C) {
      const cell_address = XLSX.utils.encode_cell({ c: C, r: R });
      if (!ws[cell_address]) continue;

      // تخطي صفوف الـ Header (تم تنسيقها بالفعل)
      if (R < headerRows) continue;

      // تنسيق صف العناوين (الصف السابع)
      if (R === headerRows) {
        ws[cell_address].s = {
          font: {
            bold: true,
            color: { rgb: "FFFFFF" },
            size: 12,
            name: "Arial"
          },
          fill: {
            fgColor: { rgb: "1F4E79" }  // أزرق داكن احترافي
          },
          alignment: {
            horizontal: "center",
            vertical: "center",
            readingOrder: 2  // RTL
          },
          border: {
            top: { style: "thick", color: { rgb: "000000" } },
            bottom: { style: "thick", color: { rgb: "000000" } },
            left: { style: "thick", color: { rgb: "000000" } },
            right: { style: "thick", color: { rgb: "000000" } }
          }
        };
      } else {
        // تنسيق البيانات (الصفوف الأخرى)
        const isEvenRow = (R - headerRows) % 2 === 1;
        ws[cell_address].s = {
          font: {
            size: 11,
            name: "Arial",
            color: { rgb: "000000" }
          },
          fill: {
            fgColor: { rgb: isEvenRow ? "F8F9FA" : "FFFFFF" }  // تبديل الألوان
          },
          alignment: {
            horizontal: C === 0 ? "center" : "right",  // الرقم في الوسط، النصوص يمين
            vertical: "center",
            readingOrder: 2,  // RTL
            wrapText: true
          },
          border: {
            top: { style: "thin", color: { rgb: "000000" } },
            bottom: { style: "thin", color: { rgb: "000000" } },
            left: { style: "thin", color: { rgb: "000000" } },
            right: { style: "thin", color: { rgb: "000000" } }
          }
        };
      }
    }
  }

  // إنشاء جدول Excel احترافي
  const tableRange = `A${headerRows + 1}:${XLSX.utils.encode_col(range.e.c)}${range.e.r + 1}`;
  ws['!autofilter'] = { ref: tableRange };

  // إعداد اتجاه الورقة RTL
  ws['!dir'] = 'rtl';

  // تجميد الصفوف (Header + عناوين الأعمدة)
  ws['!freeze'] = { xSplit: 0, ySplit: headerRows + 1 };

  // إعداد الطباعة
  ws['!margins'] = {
    left: 0.5,
    right: 0.5,
    top: 0.75,
    bottom: 0.75,
    header: 0.3,
    footer: 0.3
  };

  // إعداد اتجاه الطباعة
  ws['!printOptions'] = {
    orientation: 'landscape',  // طباعة أفقية
    scale: 75,  // تصغير للملائمة
    fitToWidth: 1,
    fitToHeight: 0,
    headings: true,
    gridLines: true
  };
}

// دالة التنسيق الاحترافي لـ Excel مع جدول احترافي (للتوافق مع الكود القديم)
function applyProfessionalFormatting(ws, dataLength) {
  const range = XLSX.utils.decode_range(ws['!ref']);

  // تعيين عرض الأعمدة بشكل احترافي (من اليمين لليسار)
  ws['!cols'] = [
    { wch: 8 },   // الرقم
    { wch: 25 },  // الاسم الرباعي حصراً
    { wch: 25 },  // اسم الأم الثلاثي حصراً
    { wch: 10 },  // الجنس
    { wch: 15 },  // تاريخ الميلاد
    { wch: 20 },  // رقم البطاقة الموحدة / الجنسية
    { wch: 15 },  // رقم الهاتف
    { wch: 18 },  // رقم امر المباشرة
    { wch: 18 },  // تاريخ امر المباشرة
    { wch: 30 },  // الدائرة
    { wch: 25 },  // التخصص
    { wch: 15 }   // تاريخ الإدخال
  ];

  // تطبيق التنسيق على كل خلية مع جدول احترافي
  for (let R = range.s.r; R <= range.e.r; ++R) {
    for (let C = range.s.c; C <= range.e.c; ++C) {
      const cell_address = XLSX.utils.encode_cell({ c: C, r: R });
      if (!ws[cell_address]) continue;

      // تنسيق الرأس (الصف الأول) - جدول احترافي
      if (R === 0) {
        ws[cell_address].s = {
          font: {
            bold: true,
            color: { rgb: "FFFFFF" },
            size: 12,
            name: "Arial"
          },
          fill: {
            fgColor: { rgb: "4472C4" }  // أزرق Excel الاحترافي
          },
          alignment: {
            horizontal: "center",
            vertical: "center",
            readingOrder: 2  // RTL
          },
          border: {
            top: { style: "thick", color: { rgb: "000000" } },
            bottom: { style: "thick", color: { rgb: "000000" } },
            left: { style: "thick", color: { rgb: "000000" } },
            right: { style: "thick", color: { rgb: "000000" } }
          }
        };
      } else {
        // تنسيق البيانات (الصفوف الأخرى) - جدول احترافي
        const isEvenRow = R % 2 === 0;
        ws[cell_address].s = {
          font: {
            size: 11,
            name: "Arial",
            color: { rgb: "000000" }
          },
          fill: {
            fgColor: { rgb: isEvenRow ? "F2F2F2" : "FFFFFF" }  // تبديل الألوان
          },
          alignment: {
            horizontal: C === 0 ? "center" : "right",  // الرقم في الوسط، النصوص يمين
            vertical: "center",
            readingOrder: 2,  // RTL
            wrapText: true
          },
          border: {
            top: { style: "thin", color: { rgb: "000000" } },
            bottom: { style: "thin", color: { rgb: "000000" } },
            left: { style: "thin", color: { rgb: "000000" } },
            right: { style: "thin", color: { rgb: "000000" } }
          }
        };
      }
    }
  }

  // إنشاء جدول Excel احترافي
  const tableRange = `A1:${XLSX.utils.encode_col(range.e.c)}${range.e.r + 1}`;
  ws['!autofilter'] = { ref: tableRange };

  // إعداد اتجاه الورقة RTL
  ws['!dir'] = 'rtl';

  // تجميد الصف الأول (الرأس)
  ws['!freeze'] = { xSplit: 0, ySplit: 1 };

  // إعداد الطباعة
  ws['!margins'] = {
    left: 0.7,
    right: 0.7,
    top: 0.75,
    bottom: 0.75,
    header: 0.3,
    footer: 0.3
  };

  // إعداد اتجاه الطباعة
  ws['!printOptions'] = {
    orientation: 'landscape',  // طباعة أفقية
    scale: 85,  // تصغير للملائمة
    fitToWidth: 1,
    fitToHeight: 0
  };
}

// دالة إنشاء البيانات مع ترتيب RTL احترافي
function createRTLExcelData(employees) {
  return employees.map((emp, index) => {
    // ترتيب البيانات من اليمين لليسار (RTL)
    const rtlData = {};

    // الأعمدة بالترتيب الصحيح من اليمين لليسار
    rtlData['الرقم'] = index + 1;
    rtlData['الاسم الرباعي حصراً'] = emp.fullName || '';
    rtlData['اسم الأم الثلاثي حصراً'] = emp.motherName || '';
    rtlData['الجنس'] = emp.gender === 'male' ? 'ذكر' : 'أنثى';
    rtlData['تاريخ الميلاد'] = emp.birthDate ? new Date(emp.birthDate).toLocaleDateString('en-US') : '';
    rtlData['رقم البطاقة الموحدة / الجنسية'] = emp.nationalId || '';
    rtlData['رقم الهاتف'] = emp.phoneNumber || '';
    rtlData['رقم امر المباشرة'] = emp.transferOrderNumber || '';
    rtlData['تاريخ امر المباشرة'] = emp.orderStartDate || '';
    rtlData['الدائرة التي تم المباشرة بها'] = emp.recycledCircle || '';
    rtlData['التخصص الدقيق'] = emp.specialization || '';
    rtlData['تاريخ الإدخال'] = emp.createdAt ? new Date(emp.createdAt).toLocaleDateString('en-US') : '';

    return rtlData;
  });
}

// دالة إنشاء Header احترافي
function createExcelHeader(ws, filterInfo = '') {
  const currentDate = new Date().toLocaleDateString('ar-SA');
  const currentTime = new Date().toLocaleTimeString('ar-SA');

  // إدراج صفوف فارغة في البداية للـ Header
  XLSX.utils.sheet_add_aoa(ws, [
    [], [], [], [], [], []  // 6 صفوف فارغة للـ Header
  ], { origin: 'A1' });

  // تحديث المرجع
  const range = XLSX.utils.decode_range(ws['!ref']);
  range.s.r = 0;
  ws['!ref'] = XLSX.utils.encode_range(range);

  // إضافة محتوى الـ Header
  ws['A1'] = { v: 'جمهورية العراق', t: 's' };
  ws['A2'] = { v: 'محافظة الديوانية', t: 's' };
  ws['A3'] = { v: 'نظام إدخال بيانات عقود الموظفين', t: 's' };
  ws['A4'] = { v: filterInfo || 'تقرير شامل لجميع الموظفين', t: 's' };
  ws['A5'] = { v: `التاريخ: ${currentDate} | الوقت: ${currentTime}`, t: 's' };
  ws['A6'] = { v: 'المبرمج المحاسب: علي عاجل خشان المحنة', t: 's' };

  // تنسيق الـ Header
  const headerStyles = {
    A1: {
      font: { bold: true, size: 16, color: { rgb: "1F4E79" } },
      alignment: { horizontal: "center", vertical: "center" },
      fill: { fgColor: { rgb: "E7F3FF" } }
    },
    A2: {
      font: { bold: true, size: 14, color: { rgb: "1F4E79" } },
      alignment: { horizontal: "center", vertical: "center" },
      fill: { fgColor: { rgb: "E7F3FF" } }
    },
    A3: {
      font: { bold: true, size: 18, color: { rgb: "FFFFFF" } },
      alignment: { horizontal: "center", vertical: "center" },
      fill: { fgColor: { rgb: "4472C4" } }
    },
    A4: {
      font: { bold: true, size: 14, color: { rgb: "4472C4" } },
      alignment: { horizontal: "center", vertical: "center" },
      fill: { fgColor: { rgb: "F0F4FF" } }
    },
    A5: {
      font: { size: 12, color: { rgb: "666666" } },
      alignment: { horizontal: "center", vertical: "center" }
    },
    A6: {
      font: { bold: true, size: 12, color: { rgb: "4472C4" } },
      alignment: { horizontal: "center", vertical: "center" },
      fill: { fgColor: { rgb: "FFF2CC" } }
    }
  };

  // تطبيق التنسيق
  Object.keys(headerStyles).forEach(cell => {
    if (ws[cell]) {
      ws[cell].s = headerStyles[cell];
    }
  });

  // دمج الخلايا للـ Header
  if (!ws['!merges']) ws['!merges'] = [];
  const colCount = 11; // عدد الأعمدة

  ws['!merges'].push(
    { s: { r: 0, c: 0 }, e: { r: 0, c: colCount - 1 } }, // جمهورية العراق
    { s: { r: 1, c: 0 }, e: { r: 1, c: colCount - 1 } }, // محافظة الديوانية
    { s: { r: 2, c: 0 }, e: { r: 2, c: colCount - 1 } }, // عنوان النظام
    { s: { r: 3, c: 0 }, e: { r: 3, c: colCount - 1 } }, // نوع التقرير
    { s: { r: 4, c: 0 }, e: { r: 4, c: colCount - 1 } }, // التاريخ والوقت
    { s: { r: 5, c: 0 }, e: { r: 5, c: colCount - 1 } }  // اسم المطور
  );

  return 6; // عدد صفوف الـ Header
}

// دالة إضافة Footer احترافي
function addExcelFooter(ws, dataLength, headerRows = 6) {
  const range = XLSX.utils.decode_range(ws['!ref']);
  const footerRow = range.e.r + 2; // صف فارغ ثم Footer
  const colCount = 11;

  // إضافة صف فارغ
  ws[`A${footerRow}`] = { v: '', t: 's' };

  // إضافة Footer
  const footerText = `إجمالي عدد الموظفين: ${dataLength} | تاريخ الإنشاء: ${new Date().toLocaleDateString('ar-SA')} | النظام: نظام إدخال بيانات العقود`;
  ws[`A${footerRow + 1}`] = { v: footerText, t: 's' };

  // تنسيق Footer
  ws[`A${footerRow + 1}`].s = {
    font: { bold: true, size: 11, color: { rgb: "4472C4" } },
    alignment: { horizontal: "center", vertical: "center" },
    fill: { fgColor: { rgb: "F0F4FF" } },
    border: {
      top: { style: "thick", color: { rgb: "4472C4" } },
      bottom: { style: "thick", color: { rgb: "4472C4" } },
      left: { style: "thick", color: { rgb: "4472C4" } },
      right: { style: "thick", color: { rgb: "4472C4" } }
    }
  };

  // دمج خلايا Footer
  if (!ws['!merges']) ws['!merges'] = [];
  ws['!merges'].push({
    s: { r: footerRow + 1, c: 0 },
    e: { r: footerRow + 1, c: colCount - 1 }
  });

  // تحديث المرجع
  range.e.r = footerRow + 1;
  ws['!ref'] = XLSX.utils.encode_range(range);
}

// دالة إنشاء ورقة Excel احترافية مع Header وFooter وجدول
function createProfessionalExcelSheet(data, sheetName, filterInfo = '') {
  // إنشاء الورقة بدون Header أولاً
  const ws = XLSX.utils.json_to_sheet(data, { origin: 'A7' });

  // إضافة الـ Header
  const headerRows = createExcelHeader(ws, filterInfo);

  // تطبيق التنسيق الاحترافي مع تعديل للـ Header
  applyAdvancedProfessionalFormatting(ws, data.length, headerRows);

  // إضافة Footer احترافي
  addExcelFooter(ws, data.length, headerRows);

  return ws;
}

// دالة لإنشاء Device ID فريد
function generateDeviceId() {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  ctx.textBaseline = 'top';
  ctx.font = '14px Arial';
  ctx.fillText('Device fingerprint', 2, 2);

  const fingerprint = canvas.toDataURL() +
    navigator.userAgent +
    navigator.language +
    screen.width + 'x' + screen.height +
    new Date().getTimezoneOffset() +
    (navigator.platform || '') +
    (navigator.cookieEnabled ? '1' : '0');

  // تحويل إلى hash بسيط
  let hash = 0;
  for (let i = 0; i < fingerprint.length; i++) {
    const char = fingerprint.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // تحويل إلى 32bit integer
  }
  return Math.abs(hash).toString(36);
}

function App() {
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [employees, setEmployees] = useState([]);
  const [deviceId, setDeviceId] = useState('');
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [syncStatus, setSyncStatus] = useState('synced'); // 'synced', 'syncing', 'offline'

  useEffect(() => {
    // إنشاء أو استرجاع Device ID
    let storedDeviceId = localStorage.getItem('deviceId');
    if (!storedDeviceId) {
      storedDeviceId = generateDeviceId();
      localStorage.setItem('deviceId', storedDeviceId);
    }
    setDeviceId(storedDeviceId);

    // فحص إذا كان هذا الجهاز قد أدخل بيانات من قبل
    const submittedDevices = JSON.parse(localStorage.getItem('submittedDevices') || '[]');
    setHasSubmitted(submittedDevices.includes(storedDeviceId));

    // استرجاع بيانات الموظفين المحفوظة
    const savedEmployees = JSON.parse(localStorage.getItem('employees') || '[]');
    setEmployees(savedEmployees);
  }, []);

  useEffect(() => {
    // إدارة شاشة التحميل مع الانترو الجميل
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 8000); // 8 ثوان للعرض الكامل

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // مراقبة حالة الاتصال
    const handleOnline = () => {
      setIsOnline(true);
      setSyncStatus('syncing');
    };

    const handleOffline = () => {
      setIsOnline(false);
      setSyncStatus('offline');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  useEffect(() => {
    // جلب البيانات من Firebase
    const loadEmployees = async () => {
      if (!isOnline) {
        // استخدام البيانات المحلية عند عدم وجود اتصال
        const savedEmployees = JSON.parse(localStorage.getItem('employees') || '[]');
        setEmployees(savedEmployees);
        setSyncStatus('offline');
        return;
      }

      try {
        setSyncStatus('syncing');
        const result = await getAllEmployees();
        if (result.success) {
          setEmployees(result.data);
          // حفظ نسخة احتياطية في localStorage
          localStorage.setItem('employees', JSON.stringify(result.data));
          setSyncStatus('synced');
        } else {
          console.error('خطأ في جلب البيانات:', result.error);
          // استخدام البيانات المحلية كبديل
          const savedEmployees = JSON.parse(localStorage.getItem('employees') || '[]');
          setEmployees(savedEmployees);
          setSyncStatus('offline');
        }
      } catch (error) {
        console.error('خطأ في الاتصال:', error);
        // استخدام البيانات المحلية كبديل
        const savedEmployees = JSON.parse(localStorage.getItem('employees') || '[]');
        setEmployees(savedEmployees);
        setSyncStatus('offline');
      }
    };

    loadEmployees();

    // الاستماع للتغييرات في الوقت الفعلي (فقط عند وجود اتصال)
    let unsubscribe;
    if (isOnline) {
      unsubscribe = subscribeToEmployees((updatedEmployees) => {
        setEmployees(updatedEmployees);
        localStorage.setItem('employees', JSON.stringify(updatedEmployees));
        setSyncStatus('synced');
      });
    }

    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, [isOnline]);

  const addEmployee = async (employee) => {
    if (hasSubmitted) {
      alert('⚠️ تم إدخال بيانات من هذا الجهاز مسبقاً!\nلا يمكن إدخال بيانات أكثر من مرة واحدة من نفس الجهاز.');
      return false;
    }

    // تكوين تاريخ الميلاد من الحقول المنفصلة
    let birthDate = '';
    if (employee.birthDay && employee.birthMonth && employee.birthYear) {
      birthDate = `${employee.birthYear}-${employee.birthMonth.padStart(2, '0')}-${employee.birthDay.padStart(2, '0')}`;
    }

    const newEmployee = {
      ...employee,
      birthDate: birthDate, // إضافة التاريخ المكون
      deviceId: deviceId
    };

    try {
      setSyncStatus('syncing');

      if (isOnline) {
        // حفظ في Firebase عند وجود اتصال
        const result = await addEmployeeToFirebase(newEmployee);

        if (result.success) {
          // تحديث الحالة المحلية
          const employeeWithId = { ...newEmployee, id: result.id };
          const updatedEmployees = [...employees, employeeWithId];
          setEmployees(updatedEmployees);

          // حفظ نسخة احتياطية في localStorage
          localStorage.setItem('employees', JSON.stringify(updatedEmployees));

          // إضافة Device ID إلى قائمة الأجهزة التي أدخلت بيانات
          const submittedDevices = JSON.parse(localStorage.getItem('submittedDevices') || '[]');
          submittedDevices.push(deviceId);
          localStorage.setItem('submittedDevices', JSON.stringify(submittedDevices));

          setHasSubmitted(true);
          setSyncStatus('synced');
          return true;
        } else {
          setSyncStatus('offline');
          alert('❌ خطأ في حفظ البيانات: ' + result.error);
          return false;
        }
      } else {
        // حفظ محلي فقط عند عدم وجود اتصال
        const employeeWithId = { ...newEmployee, id: Date.now().toString() };
        const updatedEmployees = [...employees, employeeWithId];
        setEmployees(updatedEmployees);

        // حفظ في localStorage مع علامة للمزامنة لاحقاً
        const pendingSync = JSON.parse(localStorage.getItem('pendingSync') || '[]');
        pendingSync.push(employeeWithId);
        localStorage.setItem('pendingSync', JSON.stringify(pendingSync));
        localStorage.setItem('employees', JSON.stringify(updatedEmployees));

        // إضافة Device ID إلى قائمة الأجهزة التي أدخلت بيانات
        const submittedDevices = JSON.parse(localStorage.getItem('submittedDevices') || '[]');
        submittedDevices.push(deviceId);
        localStorage.setItem('submittedDevices', JSON.stringify(submittedDevices));

        setHasSubmitted(true);
        setSyncStatus('offline');
        alert('✅ تم حفظ البيانات محلياً!\n🔄 سيتم رفعها للسحابة عند توفر الاتصال.');
        return true;
      }
    } catch (error) {
      console.error('خطأ في إضافة الموظف:', error);
      setSyncStatus('offline');
      alert('❌ خطأ في الاتصال بقاعدة البيانات');
      return false;
    }
  };

  const deleteEmployee = async (employeeId, password) => {
    const savedDeletePassword = localStorage.getItem('deletePassword') || 'a1234a1234A@#1';
    if (password !== savedDeletePassword) {
      alert('❌ كلمة المرور غير صحيحة!\nلا يمكن حذف القيد.');
      return false;
    }

    const confirmDelete = confirm('⚠️ تأكيد الحذف\n\nهل أنت متأكد من حذف هذا القيد؟\nهذا الإجراء لا يمكن التراجع عنه!');

    if (!confirmDelete) {
      return false;
    }

    try {
      setSyncStatus('syncing');

      if (isOnline) {
        // حذف من Firebase
        const { deleteEmployee: deleteFromFirebase } = await import('./firebase/employeeService.js');
        const result = await deleteFromFirebase(employeeId);

        if (result.success) {
          // تحديث الحالة المحلية
          const updatedEmployees = employees.filter(emp => emp.id !== employeeId);
          setEmployees(updatedEmployees);
          localStorage.setItem('employees', JSON.stringify(updatedEmployees));
          setSyncStatus('synced');
          alert('✅ تم حذف القيد بنجاح من قاعدة البيانات!');
          return true;
        } else {
          setSyncStatus('offline');
          alert('❌ خطأ في حذف القيد: ' + result.error);
          return false;
        }
      } else {
        // حذف محلي فقط عند عدم وجود اتصال
        const updatedEmployees = employees.filter(emp => emp.id !== employeeId);
        setEmployees(updatedEmployees);
        localStorage.setItem('employees', JSON.stringify(updatedEmployees));

        // إضافة للقائمة المؤجلة للحذف
        const pendingDeletes = JSON.parse(localStorage.getItem('pendingDeletes') || '[]');
        pendingDeletes.push(employeeId);
        localStorage.setItem('pendingDeletes', JSON.stringify(pendingDeletes));

        setSyncStatus('offline');
        alert('✅ تم حذف القيد محلياً!\n🔄 سيتم حذفه من السحابة عند توفر الاتصال.');
        return true;
      }
    } catch (error) {
      console.error('خطأ في حذف الموظف:', error);
      setSyncStatus('offline');
      alert('❌ خطأ في حذف القيد');
      return false;
    }
  };

  const renderPage = () => {
    switch(currentPage) {
      case 'data-entry':
        return <DataEntryPage onAddEmployee={addEmployee} onNavigate={setCurrentPage} hasSubmitted={hasSubmitted} deviceId={deviceId} syncStatus={syncStatus} isOnline={isOnline} employees={employees} />;
      case 'reports':
        return <ReportsPage employees={employees} onNavigate={setCurrentPage} syncStatus={syncStatus} isOnline={isOnline} />;
      case 'employees':
        return <EmployeesPage employees={employees} onNavigate={setCurrentPage} onDeleteEmployee={deleteEmployee} syncStatus={syncStatus} isOnline={isOnline} />;
      case 'filter':
        return <FilterPage employees={employees} onNavigate={setCurrentPage} syncStatus={syncStatus} isOnline={isOnline} />;
      case 'settings':
        return <SettingsPage onNavigate={setCurrentPage} syncStatus={syncStatus} isOnline={isOnline} />;
      default:
        return <DashboardPage employees={employees} onNavigate={setCurrentPage} syncStatus={syncStatus} isOnline={isOnline} />;
    }
  };

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <div className="app">
      {renderPage()}
      {currentPage !== 'dashboard' && (
        <BottomNav currentPage={currentPage} onNavigate={setCurrentPage} />
      )}
    </div>
  );
}

// Static Intro Screen
function LoadingScreen() {
  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100%',
      height: '100vh',
      background: 'linear-gradient(135deg, #0066ff 0%, #004db3 100%)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 10000,
      color: 'white',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{
        textAlign: 'center',
        maxWidth: '800px',
        padding: '40px'
      }}>
        <div style={{
          fontSize: '4rem',
          fontWeight: '900',
          marginBottom: '30px',
          textShadow: '0 4px 8px rgba(0,0,0,0.3)'
        }}>
          بيانات العقود المؤقتة
        </div>

        <div style={{
          fontSize: '2rem',
          fontWeight: '700',
          marginBottom: '20px',
          opacity: '0.9'
        }}>
          مدير قسم العقود: حيدر كريم لفتة
        </div>

        <div style={{
          fontSize: '1.8rem',
          fontWeight: '600',
          opacity: '0.8'
        }}>
          إعداد وبرمجة المحاسب: علي عاجل خشان المحنة
        </div>
      </div>
    </div>
  );
}

// Dashboard Component
function DashboardPage({ employees, onNavigate }) {
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState(null);

  const cards = [
    { id: 'data-entry', title: 'البيانات', icon: '📋', color: 'linear-gradient(135deg, #ff6b35, #f7931e)', size: 'small' },
    { id: 'reports', title: 'التقارير', icon: '📊', color: 'linear-gradient(135deg, #667eea, #764ba2)', size: 'small', protected: true },
    { id: 'employees', title: 'الموظفين', icon: '👥', color: 'linear-gradient(135deg, #f093fb, #f5576c)', size: 'small', protected: true },
    { id: 'filter', title: 'التصفية', icon: '🔍', color: 'linear-gradient(135deg, #4facfe, #00f2fe)', animation: 'gradientShift 3s ease-in-out infinite', size: 'small', protected: true },
    { id: 'settings', title: 'الإعدادات', icon: '⚙️', color: 'linear-gradient(135deg, #43e97b, #38f9d7)', size: 'small', protected: true },
    { id: 'exit', title: 'إغلاق البرنامج', icon: '🚪', color: 'linear-gradient(135deg, #fa709a, #fee140)', size: 'small' }
  ];

  const handleCardClick = (cardId) => {
    if (cardId === 'exit') {
      if (confirm('هل تريد إغلاق البرنامج والخروج من المتصفح؟')) {
        window.close();
        // إذا لم يتم إغلاق النافذة، حاول إعادة التوجيه
        setTimeout(() => {
          window.location.href = 'about:blank';
        }, 100);
      }
    } else {
      const card = cards.find(c => c.id === cardId);
      if (card && card.protected) {
        setPendingNavigation(cardId);
        setShowPasswordModal(true);
      } else {
        onNavigate(cardId);
      }
    }
  };

  const handlePasswordSubmit = (password) => {
    if (password === '1000') {
      setShowPasswordModal(false);
      if (pendingNavigation) {
        onNavigate(pendingNavigation);
        setPendingNavigation(null);
      }
    } else {
      alert('❌ كلمة المرور غير صحيحة!');
    }
  };

  const handlePasswordClose = () => {
    setShowPasswordModal(false);
    setPendingNavigation(null);
  };



  return (
    <div className="container dashboard-container">

      <div className="header">
        <div style={{ marginTop: '15px', color: '#2c3e50', fontWeight: '600', fontSize: '1.2rem' }}>
          إجمالي الموظفين المسجلين: {convertToArabicNumbers(employees.length)}
        </div>
      </div>

      <div className="dashboard-grid">
        {cards.map(card => (
          <div
            key={card.id}
            className="dashboard-card"
            style={{
              background: card.color,
              backgroundSize: card.animation ? '300% 300%' : 'auto',
              animation: card.animation || 'none'
            }}
            onClick={() => handleCardClick(card.id)}
          >
            <div className="icon" style={{ fontSize: card.size === 'small' ? '2rem' : '3rem' }}>{card.icon}</div>
            <h3>{card.title}</h3>
            {card.protected && (
              <div style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                background: 'rgba(255,255,255,0.9)',
                borderRadius: '50%',
                width: '25px',
                height: '25px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '12px'
              }}>
                🔒
              </div>
            )}
          </div>
        ))}
      </div>

      <PasswordModal
        isOpen={showPasswordModal}
        onClose={handlePasswordClose}
        onSubmit={handlePasswordSubmit}
        title="منطقة محمية"
        message="هذه الواجهة محمية بكلمة مرور. يرجى إدخال كلمة المرور للمتابعة."
      />
    </div>
  );
}

// Password Input Modal Component
function PasswordModal({ isOpen, onClose, onSubmit, title, message }) {
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(password);
    setPassword('');
    setShowPassword(false);
  };

  const handleClose = () => {
    setPassword('');
    setShowPassword(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0,0,0,0.7)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 10000
    }}>
      <div style={{
        background: 'white',
        borderRadius: '20px',
        padding: '30px',
        maxWidth: '400px',
        width: '90%',
        boxShadow: '0 20px 40px rgba(0,0,0,0.3)'
      }}>
        <h3 style={{ textAlign: 'center', marginBottom: '20px', color: '#dc3545' }}>
          🔒 {title}
        </h3>
        <p style={{ textAlign: 'center', marginBottom: '20px', color: '#666' }}>
          {message}
        </p>
        <form onSubmit={handleSubmit}>
          <div style={{ position: 'relative', marginBottom: '20px' }}>
            <input
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="أدخل كلمة المرور"
              style={{
                width: '100%',
                padding: '12px 50px 12px 15px',
                border: '2px solid #ddd',
                borderRadius: '10px',
                fontSize: '16px',
                textAlign: 'center',
                fontFamily: 'monospace'
              }}
              autoFocus
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              style={{
                position: 'absolute',
                right: '10px',
                top: '50%',
                transform: 'translateY(-50%)',
                background: 'none',
                border: 'none',
                fontSize: '18px',
                cursor: 'pointer'
              }}
            >
              {showPassword ? '🙈' : '👁️'}
            </button>
          </div>
          <div style={{ display: 'flex', gap: '10px', justifyContent: 'center' }}>
            <button
              type="button"
              onClick={handleClose}
              style={{
                padding: '10px 20px',
                background: '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer'
              }}
            >
              إلغاء
            </button>
            <button
              type="submit"
              style={{
                padding: '10px 20px',
                background: '#dc3545',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer'
              }}
            >
              تأكيد
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// Sync Status Component
function SyncStatusIndicator({ syncStatus, isOnline }) {
  const getStatusInfo = () => {
    if (!isOnline) {
      return { icon: '🔴', text: 'غير متصل', color: '#dc3545' };
    }

    switch (syncStatus) {
      case 'syncing':
        return { icon: '🔄', text: 'جاري المزامنة...', color: '#ffc107' };
      case 'synced':
        return { icon: '✅', text: 'متزامن', color: '#28a745' };
      default:
        return { icon: '⚠️', text: 'غير متزامن', color: '#fd7e14' };
    }
  };

  const status = getStatusInfo();

  return (
    <div style={{
      display: 'inline-flex',
      alignItems: 'center',
      gap: '5px',
      background: status.color,
      color: 'white',
      padding: '5px 10px',
      borderRadius: '15px',
      fontSize: '0.8rem',
      fontWeight: '600'
    }}>
      <span>{status.icon}</span>
      <span>{status.text}</span>
    </div>
  );
}

// Data Entry Component
function DataEntryPage({ onAddEmployee, onNavigate, hasSubmitted, deviceId, syncStatus, isOnline, employees }) {
  const [formData, setFormData] = useState({
    fullName: '',
    motherName: '',
    birthDay: '',
    birthMonth: '',
    birthYear: '',
    nationalId: '',
    phoneNumber: '',
    transferOrderNumber: '',
    orderStartDate: '',
    recycledCircle: '',
    specialization: '',
    gender: 'male' // إضافة حقل الجنس
  });

  const handleSubmit = (e) => {
    e.preventDefault();

    if (hasSubmitted) {
      alert('⚠️ تم إدخال بيانات من هذا الجهاز مسبقاً!\nلا يمكن إدخال بيانات أكثر من مرة واحدة من نفس الجهاز.');
      return;
    }

    // فحص التكرار للحقول المطلوبة
    const duplicateCheck = employees.find(emp =>
      emp.fullName === formData.fullName ||
      (formData.nationalId && emp.nationalId === formData.nationalId) ||
      (formData.phoneNumber && emp.phoneNumber === formData.phoneNumber)
    );

    if (duplicateCheck) {
      let duplicateField = '';
      if (duplicateCheck.fullName === formData.fullName) {
        duplicateField = 'الاسم الرباعي';
      } else if (duplicateCheck.nationalId === formData.nationalId) {
        duplicateField = 'رقم البطاقة الموحدة / الجنسية';
      } else if (duplicateCheck.phoneNumber === formData.phoneNumber) {
        duplicateField = 'رقم الهاتف';
      }

      alert(`⚠️ خطأ: ${duplicateField} مسجل مسبقاً!\n\nلا يمكن تكرار:\n• الاسم الرباعي\n• رقم البطاقة الموحدة / الجنسية\n• رقم الهاتف\n\nيرجى التحقق من البيانات المدخلة.`);
      return;
    }

    if (formData.fullName && formData.motherName) {
      const success = onAddEmployee(formData);
      if (success) {
        setFormData({
          fullName: '',
          motherName: '',
          birthDay: '',
          birthMonth: '',
          birthYear: '',
          nationalId: '',
          phoneNumber: '',
          transferOrderNumber: '',
          orderStartDate: '',
          recycledCircle: '',
          specialization: ''
        });
        alert('✅ تم حفظ البيانات بنجاح!\n🔒 تم تسجيل هذا الجهاز ولا يمكن إدخال بيانات أخرى منه.');
      }
    } else {
      alert('⚠️ يرجى ملء الحقول المطلوبة:\n• الاسم الكامل\n• اسم الأم الكامل');
    }
  };

  return (
    <div className="container data-entry-container">
      <div className="programmer-header">
        <span className="programmer-name">المبرمج المحاسب: علي عاجل خشان المحنة</span>
      </div>
      <div className="header">
        <h1 style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          fontSize: '2.2rem',
          fontWeight: '800',
          textShadow: '0 2px 4px rgba(0,0,0,0.1)',
          marginBottom: '15px'
        }}>
          📝 إدخال بيانات العقود
        </h1>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'center', flexWrap: 'wrap', justifyContent: 'center' }}>
          <SyncStatusIndicator syncStatus={syncStatus} isOnline={isOnline} />
          <div style={{
            background: hasSubmitted ? '#dc3545' : '#28a745',
            color: 'white',
            padding: '8px 15px',
            borderRadius: '20px',
            fontSize: '12px',
            fontWeight: 'bold'
          }}>
            🔒 Device ID: {deviceId.substring(0, 8)}...
          </div>
          <button className="btn btn-secondary" onClick={() => onNavigate('dashboard')}>
            العودة للرئيسية
          </button>
        </div>
      </div>

      {hasSubmitted && (
        <div style={{
          background: 'linear-gradient(135deg, #dc3545, #c82333)',
          color: 'white',
          padding: '20px',
          borderRadius: '15px',
          marginBottom: '20px',
          textAlign: 'center',
          boxShadow: '0 5px 15px rgba(220, 53, 69, 0.3)'
        }}>
          <div style={{ fontSize: '2rem', marginBottom: '10px' }}>🚫</div>
          <h3 style={{ margin: '0 0 10px 0' }}>تم إدخال بيانات من هذا الجهاز مسبقاً</h3>
          <p style={{ margin: 0, opacity: 0.9 }}>
            لا يمكن إدخال بيانات أكثر من مرة واحدة من نفس الجهاز لضمان عدم التكرار
          </p>
        </div>
      )}

      <form className="form-container" onSubmit={handleSubmit}>
        <div className="form-group required" style={{ display: 'flex', alignItems: 'flex-start', gap: '20px' }}>
          <div style={{ flex: 1 }}>
            <label>الاسم الرباعي حصراً *</label>
            <input
              type="text"
              value={formData.fullName}
              onChange={(e) => setFormData({...formData, fullName: e.target.value})}
              required
              disabled={hasSubmitted}
              style={{ opacity: hasSubmitted ? 0.5 : 1, width: '60%' }}
            />
          </div>
          <div className="gender-selection" style={{ minWidth: '200px' }}>
            <label style={{ marginBottom: '8px', display: 'block', fontWeight: 'bold' }}>الجنس:</label>
            <div style={{ display: 'flex', gap: '15px' }}>
              <label style={{ display: 'flex', alignItems: 'center', gap: '5px', cursor: 'pointer' }}>
                <input
                  type="radio"
                  name="gender"
                  value="male"
                  checked={formData.gender === 'male'}
                  onChange={(e) => setFormData({...formData, gender: e.target.value})}
                  style={{ display: 'none' }}
                  disabled={hasSubmitted}
                />
                <div className={`gender-circle ${formData.gender === 'male' ? 'selected' : ''}`}>
                  ⚪
                </div>
                <span>ذكر</span>
              </label>
              <label style={{ display: 'flex', alignItems: 'center', gap: '5px', cursor: 'pointer' }}>
                <input
                  type="radio"
                  name="gender"
                  value="female"
                  checked={formData.gender === 'female'}
                  onChange={(e) => setFormData({...formData, gender: e.target.value})}
                  style={{ display: 'none' }}
                  disabled={hasSubmitted}
                />
                <div className={`gender-circle ${formData.gender === 'female' ? 'selected' : ''}`}>
                  ⚪
                </div>
                <span>أنثى</span>
              </label>
            </div>
          </div>
        </div>

        <div className="form-group required">
          <label>اسم الأم الثلاثي حصراً *</label>
          <input
            type="text"
            value={formData.motherName}
            onChange={(e) => setFormData({...formData, motherName: e.target.value})}
            required
            disabled={hasSubmitted}
            style={{ opacity: hasSubmitted ? 0.5 : 1, width: '60%' }}
          />
        </div>

        <div className="form-group">
          <label>تاريخ الميلاد</label>
          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <div style={{ flex: 1 }}>
              <label style={{ fontSize: '0.9rem', color: '#666', marginBottom: '5px', display: 'block' }}>اليوم</label>
              <input
                type="number"
                min="1"
                max="31"
                value={formData.birthDay}
                onChange={(e) => setFormData({...formData, birthDay: e.target.value})}
                placeholder="يوم"
                style={{ textAlign: 'center' }}
              />
            </div>
            <div style={{ flex: 1 }}>
              <label style={{ fontSize: '0.9rem', color: '#666', marginBottom: '5px', display: 'block' }}>الشهر</label>
              <input
                type="number"
                min="1"
                max="12"
                value={formData.birthMonth}
                onChange={(e) => setFormData({...formData, birthMonth: e.target.value})}
                placeholder="شهر"
                style={{ textAlign: 'center' }}
              />
            </div>
            <div style={{ flex: 1 }}>
              <label style={{ fontSize: '0.9rem', color: '#666', marginBottom: '5px', display: 'block' }}>السنة</label>
              <input
                type="number"
                min="1900"
                max="2024"
                value={formData.birthYear}
                onChange={(e) => setFormData({...formData, birthYear: e.target.value})}
                placeholder="سنة"
                style={{ textAlign: 'center' }}
              />
            </div>
          </div>
        </div>

        <div className="form-group">
          <label>رقم البطاقة الموحدة / الجنسية</label>
          <input
            type="text"
            value={formData.nationalId}
            onChange={(e) => setFormData({...formData, nationalId: e.target.value})}
            maxLength="12"
            disabled={hasSubmitted}
            style={{ opacity: hasSubmitted ? 0.5 : 1, width: '50%' }}
          />
        </div>

        <div className="form-group">
          <label>رقم الهاتف</label>
          <input
            type="tel"
            value={formData.phoneNumber}
            onChange={(e) => setFormData({...formData, phoneNumber: e.target.value})}
            placeholder="07xxxxxxxxx"
            maxLength="11"
            disabled={hasSubmitted}
            style={{ opacity: hasSubmitted ? 0.5 : 1, width: '40%' }}
          />
        </div>

        <div className="form-group">
          <label>رقم امر المباشرة</label>
          <input
            type="text"
            value={formData.transferOrderNumber}
            onChange={(e) => setFormData({...formData, transferOrderNumber: e.target.value})}
            style={{ width: '25%' }}
          />
        </div>

        <div className="form-group">
          <label>تاريخ امر المباشرة</label>
          <input
            type="text"
            value={formData.orderStartDate}
            onChange={(e) => setFormData({...formData, orderStartDate: e.target.value})}
            placeholder="أدخل تاريخ امر المباشرة (مثال: 15/3/2024)"
            style={{ width: '35%' }}
          />
        </div>

        <div className="form-group">
          <label>الدائرة التي تم المباشرة بها</label>
          <textarea
            value={formData.recycledCircle}
            onChange={(e) => setFormData({...formData, recycledCircle: e.target.value})}
            rows="2"
            disabled={hasSubmitted}
            style={{ opacity: hasSubmitted ? 0.5 : 1, width: '50%', resize: 'vertical' }}
            placeholder="أدخل اسم الدائرة التي تم المباشرة بها..."
          />
        </div>

        <div style={{ display: 'flex', alignItems: 'flex-start', gap: '15px' }}>
          <div className="form-group" style={{ flex: 1 }}>
            <label>التخصص الدقيق</label>
            <input
              type="text"
              value={formData.specialization}
              onChange={(e) => setFormData({...formData, specialization: e.target.value})}
              disabled={hasSubmitted}
              style={{ opacity: hasSubmitted ? 0.5 : 1, width: '50%' }}
              placeholder="أدخل التخصص الدقيق (مثال: محاسبة مالية، هندسة مدنية...)"
            />
          </div>

          <button
            type="submit"
            className="btn btn-primary"
            disabled={hasSubmitted}
            style={{
              minWidth: '120px',
              height: '48px',
              marginTop: '28px',
              opacity: hasSubmitted ? 0.5 : 1,
              cursor: hasSubmitted ? 'not-allowed' : 'pointer'
            }}
          >
            {hasSubmitted ? '🔒 محظور' : '💾 حفظ'}
          </button>
        </div>
      </form>
    </div>
  );
}

// Reports Component
function ReportsPage({ employees, onNavigate }) {
  const [filterPassword, setFilterPassword] = React.useState('1000'); // كلمة مرور التصفية

  const checkPassword = (action) => {
    const password = prompt('أدخل كلمة المرور للوصول لهذه الميزة:');
    if (password === filterPassword) {
      if (action === 'export') {
        exportToExcel();
      } else if (action === 'print') {
        printReport();
      }
    } else if (password !== null) {
      alert('❌ كلمة المرور غير صحيحة!');
    }
  };

  const exportToExcel = () => {
    try {

    // إنشاء البيانات مع ترتيب RTL احترافي
    const exportData = createRTLExcelData(employees);

    // إنشاء ورقة Excel احترافية مع Header وجدول
    const ws = createProfessionalExcelSheet(exportData, 'بيانات الموظفين', 'تقرير شامل لجميع الموظفين المسجلين');

    const wb = XLSX.utils.book_new();

    // إعداد خصائص الكتاب
    wb.Props = {
      Title: "تقرير شامل - بيانات عقود الموظفين",
      Subject: "جميع بيانات الموظفين المسجلين في النظام",
      Author: "المبرمج المحاسب: علي عاجل خشان المحنة",
      CreatedDate: new Date(),
      Company: "محافظة الديوانية - قسم تقنية المعلومات"
    };

    XLSX.utils.book_append_sheet(wb, ws, 'بيانات الموظفين');

    // إعداد اتجاه RTL للكتاب
    wb.Workbook = {
      Views: [{
        RTL: true
      }]
    };

    XLSX.writeFile(wb, `تقرير_شامل_بيانات_الموظفين_${new Date().toLocaleDateString('ar-SA').replace(/\//g, '-')}.xlsx`);

    alert('✅ تم تصدير البيانات بنجاح!\n📊 ملف Excel احترافي جاهز للاستخدام');
    } catch (error) {
      console.error('خطأ في التصدير:', error);
      alert('❌ حدث خطأ في تصدير البيانات');
    }
  };

  const printReport = () => {
    const printWindow = window.open('', '_blank');
    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <title>تقرير الموظفين - محافظة الديوانية</title>
        <style>
          body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            margin: 20px;
            font-size: 12px;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #4472C4;
            padding-bottom: 15px;
          }
          .header h1 {
            color: #4472C4;
            margin-bottom: 10px;
            font-size: 18px;
          }
          .header p {
            color: #666;
            margin: 5px 0;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 10px;
          }
          th, td {
            border: 1px solid #4472C4;
            padding: 8px 4px;
            text-align: right;
          }
          th {
            background-color: #4472C4;
            color: white;
            font-weight: bold;
            font-size: 11px;
          }
          tr:nth-child(even) {
            background-color: #f8f9fa;
          }
          .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
          }
          @media print {
            body { margin: 0; }
            .header { page-break-inside: avoid; }
            table { page-break-inside: auto; }
            tr { page-break-inside: avoid; page-break-after: auto; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>تقرير الموظفين - محافظة الديوانية</h1>
          <p><strong>المبرمج المحاسب:</strong> علي عاجل خشان المحنة</p>
          <p><strong>تاريخ الطباعة:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
          <p><strong>إجمالي الموظفين:</strong> ${convertToArabicNumbers(employees.length)} موظف</p>
        </div>
        <table>
          <thead>
            <tr>
              <th>الرقم</th>
              <th>الاسم الكامل</th>
              <th>اسم الأم</th>
              <th>تاريخ الميلاد</th>
              <th>الرقم الوطني</th>
              <th>رقم الهاتف</th>
              <th>رقم امر المباشرة</th>
              <th>التخصص الدقيق</th>
              <th>تاريخ الإدخال</th>
            </tr>
          </thead>
          <tbody>
            ${employees.map((emp, index) => `
              <tr>
                <td>${convertToArabicNumbers(index + 1)}</td>
                <td>${emp.fullName || ''}</td>
                <td>${emp.motherName || ''}</td>
                <td>${emp.birthDate ? convertToArabicNumbers(new Date(emp.birthDate).toLocaleDateString('ar-SA')) : ''}</td>
                <td>${convertToArabicNumbers(emp.nationalId) || ''}</td>
                <td>${convertToArabicNumbers(emp.phoneNumber) || ''}</td>
                <td>${convertToArabicNumbers(emp.transferOrderNumber) || ''}</td>
                <td>${emp.specialization || ''}</td>
                <td>${convertToArabicNumbers(new Date(emp.createdAt).toLocaleDateString('ar-SA'))}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
        <div class="footer">
          <p>تم إنشاء هذا التقرير بواسطة نظام إدخال بيانات العقود - محافظة الديوانية</p>
        </div>
      </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    setTimeout(() => {
      printWindow.print();
    }, 500);
  };

  return (
    <div className="container">
      <div className="header">
        <h1>التقارير والإحصائيات</h1>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', justifyContent: 'center' }}>
          <button className="btn btn-success" onClick={() => checkPassword('export')}>
            🔒 📊 تصدير Excel (.xlsx)
          </button>
          <button className="btn btn-primary" onClick={() => checkPassword('print')}>
            🔒 🖨️ طباعة التقرير
          </button>
          <button className="btn btn-secondary" onClick={() => onNavigate('dashboard')}>
            🏠 العودة للرئيسية
          </button>
        </div>
      </div>

      <div className="table-container" style={{
        background: 'white',
        borderRadius: '15px',
        padding: '20px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
        overflow: 'auto'
      }}>
        <h3 style={{
          color: '#4472C4',
          marginBottom: '20px',
          textAlign: 'center',
          borderBottom: '2px solid #4472C4',
          paddingBottom: '10px'
        }}>
          📋 بيانات الموظفين ({convertToArabicNumbers(employees.length)})
        </h3>
        {employees.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '60px', color: '#666' }}>
            <div style={{ fontSize: '4rem', marginBottom: '20px' }}>📊</div>
            <h4>لا توجد بيانات للعرض</h4>
            <p>قم بإضافة موظفين من صفحة البيانات</p>
          </div>
        ) : (
          <div style={{ overflowX: 'auto' }}>
            <table style={{
              width: '100%',
              borderCollapse: 'collapse',
              fontSize: '14px',
              backgroundColor: 'white'
            }}>
              <thead>
                <tr style={{ backgroundColor: '#4472C4' }}>
                  <th style={{
                    border: '2px solid #2c5aa0',
                    padding: '12px 8px',
                    color: 'white',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    minWidth: '60px'
                  }}>الرقم</th>
                  <th style={{
                    border: '2px solid #2c5aa0',
                    padding: '12px 8px',
                    color: 'white',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    minWidth: '200px'
                  }}>الاسم الكامل</th>
                  <th style={{
                    border: '2px solid #2c5aa0',
                    padding: '12px 8px',
                    color: 'white',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    minWidth: '200px'
                  }}>اسم الأم الكامل</th>
                  <th style={{
                    border: '2px solid #2c5aa0',
                    padding: '12px 8px',
                    color: 'white',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    minWidth: '120px'
                  }}>تاريخ الميلاد</th>
                  <th style={{
                    border: '2px solid #2c5aa0',
                    padding: '12px 8px',
                    color: 'white',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    minWidth: '140px'
                  }}>الرقم الوطني</th>
                  <th style={{
                    border: '2px solid #2c5aa0',
                    padding: '12px 8px',
                    color: 'white',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    minWidth: '120px'
                  }}>رقم الهاتف</th>
                  <th style={{
                    border: '2px solid #2c5aa0',
                    padding: '12px 8px',
                    color: 'white',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    minWidth: '150px'
                  }}>رقم أمر المباشرة</th>
                  <th style={{
                    border: '2px solid #2c5aa0',
                    padding: '12px 8px',
                    color: 'white',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    minWidth: '150px'
                  }}>تاريخ أمر المباشرة</th>
                  <th style={{
                    border: '2px solid #2c5aa0',
                    padding: '12px 8px',
                    color: 'white',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    minWidth: '180px'
                  }}>التخصص الدقيق</th>
                  <th style={{
                    border: '2px solid #2c5aa0',
                    padding: '12px 8px',
                    color: 'white',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    minWidth: '120px'
                  }}>تاريخ الإدخال</th>
                </tr>
              </thead>
              <tbody>
                {employees.map((employee, index) => (
                  <tr key={employee.id} style={{
                    backgroundColor: index % 2 === 0 ? '#f8f9fa' : 'white',
                    transition: 'background-color 0.2s ease'
                  }}
                  onMouseEnter={(e) => e.target.parentElement.style.backgroundColor = '#e3f2fd'}
                  onMouseLeave={(e) => e.target.parentElement.style.backgroundColor = index % 2 === 0 ? '#f8f9fa' : 'white'}
                  >
                    <td style={{
                      border: '1px solid #ddd',
                      padding: '10px 8px',
                      textAlign: 'center',
                      fontWeight: 'bold',
                      color: '#4472C4'
                    }}>{convertToArabicNumbers(index + 1)}</td>
                    <td style={{
                      border: '1px solid #ddd',
                      padding: '10px 8px',
                      textAlign: 'right'
                    }}>{employee.fullName || '-'}</td>
                    <td style={{
                      border: '1px solid #ddd',
                      padding: '10px 8px',
                      textAlign: 'right'
                    }}>{employee.motherName || '-'}</td>
                    <td style={{
                      border: '1px solid #ddd',
                      padding: '10px 8px',
                      textAlign: 'center'
                    }}>{employee.birthDate ? convertToArabicNumbers(new Date(employee.birthDate).toLocaleDateString('ar-SA')) : '-'}</td>
                    <td style={{
                      border: '1px solid #ddd',
                      padding: '10px 8px',
                      textAlign: 'center',
                      fontFamily: 'monospace'
                    }}>{convertToArabicNumbers(employee.nationalId) || '-'}</td>
                    <td style={{
                      border: '1px solid #ddd',
                      padding: '10px 8px',
                      textAlign: 'center',
                      fontFamily: 'monospace'
                    }}>{convertToArabicNumbers(employee.phoneNumber) || '-'}</td>
                    <td style={{
                      border: '1px solid #ddd',
                      padding: '10px 8px',
                      textAlign: 'center'
                    }}>{convertToArabicNumbers(employee.transferOrderNumber) || '-'}</td>
                    <td style={{
                      border: '1px solid #ddd',
                      padding: '10px 8px',
                      textAlign: 'center'
                    }}>{convertToArabicNumbers(employee.orderStartDate) || '-'}</td>
                    <td style={{
                      border: '1px solid #ddd',
                      padding: '10px 8px',
                      textAlign: 'right'
                    }}>{employee.specialization || '-'}</td>
                    <td style={{
                      border: '1px solid #ddd',
                      padding: '10px 8px',
                      textAlign: 'center'
                    }}>{convertToArabicNumbers(new Date(employee.createdAt).toLocaleDateString('ar-SA'))}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}

// Employees Page Component
function EmployeesPage({ employees, onNavigate, onDeleteEmployee }) {
  const [currentEmployeeIndex, setCurrentEmployeeIndex] = React.useState(0);
  const [showDeleteModal, setShowDeleteModal] = React.useState(false);
  const [searchName, setSearchName] = React.useState('');
  const [searchResults, setSearchResults] = React.useState([]);
  const [selectedEmployee, setSelectedEmployee] = React.useState(null);
  const [isEditing, setIsEditing] = React.useState(false);
  const [editData, setEditData] = React.useState({});

  // وظيفة البحث
  const handleSearch = () => {
    if (!searchName.trim()) {
      alert('يرجى إدخال اسم للبحث');
      return;
    }

    const results = employees.filter(emp =>
      emp.fullName.toLowerCase().includes(searchName.toLowerCase())
    );

    if (results.length === 0) {
      alert('لم يتم العثور على موظف بهذا الاسم');
      setSearchResults([]);
      setSelectedEmployee(null);
    } else {
      setSearchResults(results);
      setSelectedEmployee(results[0]);
      setEditData(results[0]);
    }
  };

  // وظيفة التعديل
  const handleEdit = () => {
    const password = prompt('أدخل كلمة المرور للتعديل:');
    if (password === '1000') {
      setIsEditing(true);
    } else if (password !== null) {
      alert('❌ كلمة المرور غير صحيحة!');
    }
  };

  // حفظ التعديلات
  const handleSaveEdit = () => {
    // هنا يمكن إضافة منطق حفظ التعديلات
    alert('تم حفظ التعديلات بنجاح!');
    setIsEditing(false);
  };

  if (employees.length === 0) {
    return (
      <div className="container">
        <div className="header">
          <h1>واجهة الموظفين</h1>
          <button className="btn btn-secondary" onClick={() => onNavigate('dashboard')}>
            🏠 العودة للرئيسية
          </button>
        </div>
        <div style={{ textAlign: 'center', padding: '60px', color: '#666' }}>
          <div style={{ fontSize: '4rem', marginBottom: '20px' }}>👥</div>
          <h3>لا يوجد موظفون مسجلون</h3>
          <p>قم بإضافة موظفين من صفحة البيانات أولاً</p>
          <button
            className="btn btn-primary"
            onClick={() => onNavigate('data-entry')}
            style={{ marginTop: '20px' }}
          >
            إضافة موظف جديد
          </button>
        </div>
      </div>
    );
  }

  const currentEmployee = employees[currentEmployeeIndex];

  const goToFirst = () => setCurrentEmployeeIndex(0);
  const goToPrevious = () => setCurrentEmployeeIndex(Math.max(0, currentEmployeeIndex - 1));
  const goToNext = () => setCurrentEmployeeIndex(Math.min(employees.length - 1, currentEmployeeIndex + 1));
  const goToLast = () => setCurrentEmployeeIndex(employees.length - 1);

  const handleDeleteEmployee = async (password) => {
    const currentEmployee = employees[currentEmployeeIndex];
    const success = await onDeleteEmployee(currentEmployee.id, password);

    setShowDeleteModal(false);

    if (success) {
      // إذا تم الحذف بنجاح، انتقل للموظف التالي أو السابق
      if (employees.length > 1) {
        if (currentEmployeeIndex >= employees.length - 1) {
          // إذا كان آخر موظف، انتقل للسابق
          setCurrentEmployeeIndex(Math.max(0, currentEmployeeIndex - 1));
        }
        // إذا لم يكن آخر موظف، سيبقى في نفس الفهرس (الذي سيصبح الموظف التالي)
      } else {
        // إذا لم يعد هناك موظفين، سيتم إعادة تحميل الصفحة تلقائياً
      }
    }
  };

  const printEmployee = () => {
    const printWindow = window.open('', '_blank');
    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <title>بيانات الموظف - ${currentEmployee.fullName}</title>
        <style>
          body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            margin: 20px;
            font-size: 14px;
            line-height: 1.6;
          }
          .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #4472C4;
            padding-bottom: 20px;
          }
          .header h1 {
            color: #4472C4;
            margin-bottom: 10px;
            font-size: 24px;
          }
          .employee-card {
            border: 2px solid #4472C4;
            border-radius: 15px;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
          }
          .field-row {
            display: flex;
            margin-bottom: 15px;
            align-items: center;
          }
          .field-label {
            font-weight: bold;
            color: #4472C4;
            min-width: 180px;
            background: #4472C4;
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            margin-left: 15px;
          }
          .field-value {
            flex: 1;
            padding: 8px 15px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            min-height: 20px;
          }
          .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 20px;
          }
          @media print {
            body { margin: 0; }
            .employee-card { box-shadow: none; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>بيانات الموظف</h1>
          <p><strong>محافظة الديوانية - المبرمج المحاسب: علي عاجل خشان المحنة</strong></p>
          <p><strong>تاريخ الطباعة:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
        </div>

        <div class="employee-card">
          <div class="field-row">
            <div class="field-label">الاسم الكامل</div>
            <div class="field-value">${currentEmployee.fullName || ''}</div>
          </div>
          <div class="field-row">
            <div class="field-label">اسم الأم الكامل</div>
            <div class="field-value">${currentEmployee.motherName || ''}</div>
          </div>
          <div class="field-row">
            <div class="field-label">تاريخ الميلاد</div>
            <div class="field-value">${currentEmployee.birthDate ? new Date(currentEmployee.birthDate).toLocaleDateString('ar-SA') : ''}</div>
          </div>
          <div class="field-row">
            <div class="field-label">الرقم الوطني الموحد</div>
            <div class="field-value">${currentEmployee.nationalId || ''}</div>
          </div>
          <div class="field-row">
            <div class="field-label">رقم الهاتف</div>
            <div class="field-value">${currentEmployee.phoneNumber || ''}</div>
          </div>
          <div class="field-row">
            <div class="field-label">رقم امر المباشرة</div>
            <div class="field-value">${currentEmployee.transferOrderNumber || ''}</div>
          </div>
          <div class="field-row">
            <div class="field-label">تاريخ امر المباشرة</div>
            <div class="field-value">${currentEmployee.orderStartDate || ''}</div>
          </div>
          <div class="field-row">
            <div class="field-label">الدائرة التي تم المباشرة بها</div>
            <div class="field-value">${currentEmployee.recycledCircle || ''}</div>
          </div>
          <div class="field-row">
            <div class="field-label">التخصص الدقيق</div>
            <div class="field-value">${currentEmployee.specialization || ''}</div>
          </div>
          <div class="field-row">
            <div class="field-label">تاريخ الإدخال</div>
            <div class="field-value">${new Date(currentEmployee.createdAt).toLocaleDateString('ar-SA')}</div>
          </div>
        </div>

        <div class="footer">
          <p>تم إنشاء هذا التقرير بواسطة نظام إدخال بيانات العقود - محافظة الديوانية</p>
        </div>
      </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    setTimeout(() => {
      printWindow.print();
    }, 500);
  };

  return (
    <div className="container">
      <div className="header">
        <h1>👥 واجهة الموظفين</h1>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap', justifyContent: 'center' }}>
          <button className="btn btn-secondary" onClick={() => onNavigate('dashboard')}>
            🏠 العودة للرئيسية
          </button>
        </div>
      </div>

      {/* قسم البحث */}
      <div style={{
        background: 'white',
        borderRadius: '15px',
        padding: '20px',
        marginBottom: '20px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.1)'
      }}>
        <h3 style={{ color: '#4472C4', marginBottom: '15px' }}>🔍 البحث عن موظف</h3>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
          <input
            type="text"
            value={searchName}
            onChange={(e) => setSearchName(e.target.value)}
            placeholder="أدخل اسم الموظف للبحث..."
            style={{
              flex: 1,
              padding: '12px',
              border: '2px solid #ddd',
              borderRadius: '8px',
              fontSize: '16px'
            }}
          />
          <button
            onClick={handleSearch}
            className="btn btn-primary"
            style={{ minWidth: '100px' }}
          >
            🔍 بحث
          </button>
        </div>
      </div>

      {/* عرض بيانات الموظف */}
      {(selectedEmployee || (!selectedEmployee && employees.length > 0)) && (
        <div style={{
          background: 'white',
          borderRadius: '20px',
          padding: '30px',
          boxShadow: '0 15px 35px rgba(0,0,0,0.1)',
          margin: '20px 0'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #4472C4, #2c5aa0)',
            color: 'white',
            padding: '20px',
            borderRadius: '15px',
            textAlign: 'center',
            marginBottom: '30px'
          }}>
            <h2 style={{ margin: '0 0 10px 0', fontSize: '24px' }}>
              {selectedEmployee ? selectedEmployee.fullName : (employees[currentEmployeeIndex]?.fullName || '')}
            </h2>
            <p style={{ margin: 0, opacity: 0.9 }}>
              {selectedEmployee ? (selectedEmployee.specialization || 'غير محدد') : (employees[currentEmployeeIndex]?.specialization || 'غير محدد')}
            </p>
          </div>

          <div style={{ display: 'grid', gap: '15px' }}>
            {(() => {
              const displayEmployee = selectedEmployee || employees[currentEmployeeIndex];
              if (!displayEmployee) return null;

              const fields = [
                { key: 'fullName', label: 'الاسم الرباعي حصراً', value: displayEmployee.fullName, icon: '👤' },
                { key: 'motherName', label: 'اسم الأم الثلاثي حصراً', value: displayEmployee.motherName, icon: '👩' },
                { key: 'gender', label: 'الجنس', value: displayEmployee.gender === 'male' ? 'ذكر' : 'أنثى', icon: displayEmployee.gender === 'male' ? '👨' : '👩' },
                { key: 'birthDate', label: 'تاريخ الميلاد', value: displayEmployee.birthDate ? convertToArabicNumbers(new Date(displayEmployee.birthDate).toLocaleDateString('en-US')) : '', icon: '📅' },
                { key: 'nationalId', label: 'رقم البطاقة الموحدة / الجنسية', value: convertToArabicNumbers(displayEmployee.nationalId), icon: '🆔' },
                { key: 'phoneNumber', label: 'رقم الهاتف', value: convertToArabicNumbers(displayEmployee.phoneNumber), icon: '📱' },
                { key: 'transferOrderNumber', label: 'رقم امر المباشرة', value: convertToArabicNumbers(displayEmployee.transferOrderNumber), icon: '📄' },
                { key: 'orderStartDate', label: 'تاريخ امر المباشرة', value: convertToArabicNumbers(displayEmployee.orderStartDate || ''), icon: '📅' },
                { key: 'recycledCircle', label: 'الدائرة التي تم المباشرة بها', value: displayEmployee.recycledCircle, icon: '🏢' },
                { key: 'specialization', label: 'التخصص الدقيق', value: displayEmployee.specialization, icon: '🎓' },
                { key: 'createdAt', label: 'تاريخ الإدخال', value: convertToArabicNumbers(new Date(displayEmployee.createdAt).toLocaleDateString('en-US')), icon: '⏰', readonly: true }
              ];

              return fields.map((field, index) => (
                <div key={index} style={{
                  display: 'flex',
                  alignItems: 'center',
                  padding: '15px',
                  background: index % 2 === 0 ? '#f8f9fa' : 'white',
                  borderRadius: '10px',
                  border: '1px solid #e9ecef'
                }}>
                  <div style={{
                    fontSize: '20px',
                    marginLeft: '15px',
                    minWidth: '30px'
                  }}>
                    {field.icon}
                  </div>
                  <div style={{
                    fontWeight: 'bold',
                    color: '#4472C4',
                    minWidth: '180px',
                    marginLeft: '15px'
                  }}>
                    {field.label}:
                  </div>
                  <div style={{ flex: 1 }}>
                    {isEditing && !field.readonly ? (
                      field.key === 'gender' ? (
                        <select
                          value={editData[field.key] || ''}
                          onChange={(e) => setEditData({...editData, [field.key]: e.target.value})}
                          style={{
                            width: '100%',
                            padding: '8px 12px',
                            border: '2px solid #4472C4',
                            borderRadius: '5px'
                          }}
                        >
                          <option value="male">ذكر</option>
                          <option value="female">أنثى</option>
                        </select>
                      ) : (
                        <input
                          type="text"
                          value={editData[field.key] || ''}
                          onChange={(e) => setEditData({...editData, [field.key]: e.target.value})}
                          style={{
                            width: '100%',
                            padding: '8px 12px',
                            border: '2px solid #4472C4',
                            borderRadius: '5px'
                          }}
                        />
                      )
                    ) : (
                      <div style={{
                        padding: '8px 12px',
                        background: 'white',
                        borderRadius: '5px',
                        border: '1px solid #ddd',
                        minHeight: '20px'
                      }}>
                        {field.value || '-'}
                      </div>
                    )}
                  </div>
                </div>
              ));
            })()}
          </div>
        </div>
      )}

      {/* أزرار التحكم */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        gap: '10px',
        flexWrap: 'wrap',
        marginTop: '30px',
        marginBottom: '120px'
      }}>
        {!selectedEmployee && (
          <>
            <button
              className="btn btn-primary"
              onClick={goToFirst}
              disabled={currentEmployeeIndex === 0}
              style={{ minWidth: '120px' }}
            >
              ⏮️ الأول
            </button>
            <button
              className="btn btn-primary"
              onClick={goToNext}
              disabled={currentEmployeeIndex === employees.length - 1}
              style={{ minWidth: '120px' }}
            >
              التالي ⏩
            </button>
            <button
              className="btn btn-primary"
              onClick={goToPrevious}
              disabled={currentEmployeeIndex === 0}
              style={{ minWidth: '120px' }}
            >
              ⏪ السابق
            </button>
            <button
              className="btn btn-primary"
              onClick={goToLast}
              disabled={currentEmployeeIndex === employees.length - 1}
              style={{ minWidth: '120px' }}
            >
              الأخير ⏭️
            </button>
          </>
        )}

        <button
          className="btn btn-success"
          onClick={printEmployee}
          style={{
            minWidth: '120px',
            background: 'linear-gradient(135deg, #28a745, #20c997)',
            border: 'none',
            color: 'white',
            fontWeight: 'bold'
          }}
        >
          🖨️ طباعة
        </button>
        <button
          className="btn btn-danger"
          onClick={() => setShowDeleteModal(true)}
          style={{
            minWidth: '120px',
            background: 'linear-gradient(135deg, #dc3545, #c82333)',
            border: 'none',
            color: 'white',
            fontWeight: 'bold'
          }}
        >
          🗑️ حذف قيد
        </button>

        {/* زر التعديل المتحرك - آخر زر إلى اليسار */}
        <button
          onClick={isEditing ? handleSaveEdit : handleEdit}
          style={{
            minWidth: '120px',
            padding: '12px 24px',
            border: 'none',
            borderRadius: '25px',
            fontSize: '16px',
            fontWeight: 'bold',
            cursor: 'pointer',
            background: isEditing
              ? 'linear-gradient(135deg, #28a745, #20c997)'
              : 'linear-gradient(135deg, #ffc107, #ff8c00, #ff6b6b)',
            color: 'white',
            animation: isEditing ? 'none' : 'editButtonPulse 2s ease-in-out infinite',
            transition: 'all 0.3s ease',
            boxShadow: '0 4px 15px rgba(255, 193, 7, 0.4)'
          }}
        >
          {isEditing ? '💾 حفظ التعديل' : '✏️ تعديل'}
        </button>
      </div>

      <PasswordModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onSubmit={handleDeleteEmployee}
        title="حذف قيد الموظف"
        message="أدخل كلمة المرور للمتابعة (مطلوب للحماية من التلاعب)"
      />
    </div>
  );
}

// Filter Page Component
function FilterPage({ employees, onNavigate }) {
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [filterPassword, setFilterPassword] = useState(localStorage.getItem('filterPassword') || '1000');

  const handlePasswordSubmit = (password) => {
    if (password === filterPassword) {
      setShowPasswordModal(false);
      setShowFilterModal(true);
    } else {
      alert('❌ كلمة المرور غير صحيحة!');
    }
  };

  return (
    <div className="container" style={{ marginBottom: '120px' }}>
      <div className="header">
        <h1 style={{
          background: 'linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57)',
          backgroundSize: '300% 300%',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          animation: 'gradientShift 3s ease-in-out infinite',
          fontSize: '2.2rem',
          fontWeight: '800',
          textShadow: '0 2px 4px rgba(0,0,0,0.1)'
        }}>
          🔍 التصفية حسب
        </h1>
        <button className="btn btn-secondary" onClick={() => onNavigate('dashboard')}>
          العودة للرئيسية
        </button>
      </div>

      <div className="card">
        <div style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
          borderRadius: '15px',
          padding: '30px',
          margin: '20px 0',
          color: 'white',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          boxShadow: '0 15px 35px rgba(102, 126, 234, 0.4)',
          animation: 'pulse 2s ease-in-out infinite alternate'
        }}
        onClick={() => setShowPasswordModal(true)}
        onMouseEnter={(e) => {
          e.target.style.transform = 'translateY(-5px) scale(1.02)';
          e.target.style.boxShadow = '0 20px 40px rgba(102, 126, 234, 0.6)';
        }}
        onMouseLeave={(e) => {
          e.target.style.transform = 'translateY(0) scale(1)';
          e.target.style.boxShadow = '0 15px 35px rgba(102, 126, 234, 0.4)';
        }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '20px' }}>
            <div style={{ fontSize: '3rem' }}>🔍</div>
            <div>
              <h3 style={{ margin: '0 0 10px 0', color: 'white' }}>إعداد التصفية العامة</h3>
              <p style={{ margin: 0, opacity: 0.9, fontSize: '1.1rem' }}>
                تصفية البيانات حسب التخصص والدائرة
              </p>
            </div>
            <div style={{ marginLeft: 'auto', fontSize: '2rem' }}>🔒</div>
          </div>
        </div>

        <div style={{ marginTop: '30px', textAlign: 'center' }}>
          <h4>📊 إحصائيات سريعة:</h4>
          <div style={{ display: 'flex', justifyContent: 'space-around', flexWrap: 'wrap', gap: '15px', marginTop: '20px' }}>
            <div style={{ background: '#e3f2fd', padding: '15px', borderRadius: '10px', minWidth: '120px' }}>
              <div style={{ fontSize: '1.5rem', color: '#1976d2' }}>👥</div>
              <div style={{ fontWeight: 'bold', color: '#1976d2' }}>{convertToArabicNumbers(employees.length)}</div>
              <div style={{ fontSize: '0.9rem', color: '#666' }}>إجمالي الموظفين</div>
            </div>
          </div>
        </div>
      </div>

      <PasswordModal
        isOpen={showPasswordModal}
        onClose={() => setShowPasswordModal(false)}
        onSubmit={handlePasswordSubmit}
        title="الوصول للتصفية"
        message="أدخل كلمة المرور للوصول لإعدادات التصفية"
      />

      {showFilterModal && (
        <FilterModal
          employees={employees}
          onClose={() => setShowFilterModal(false)}
        />
      )}
    </div>
  );
}

// Filter Modal Component
function FilterModal({ employees, onClose }) {
  const [specializationFilter, setSpecializationFilter] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('');
  const [genderFilter, setGenderFilter] = useState('');
  const [administrativeUnitFilter, setAdministrativeUnitFilter] = useState('');
  const [filteredEmployees, setFilteredEmployees] = useState([]);
  const [showPreview, setShowPreview] = useState(false);

  const applyFilters = () => {
    let filtered = [...employees];

    if (specializationFilter.trim()) {
      filtered = filtered.filter(emp =>
        emp.specialization && emp.specialization.toLowerCase().includes(specializationFilter.toLowerCase())
      );
    }

    if (departmentFilter.trim()) {
      filtered = filtered.filter(emp =>
        emp.recycledCircle && emp.recycledCircle.toLowerCase().includes(departmentFilter.toLowerCase())
      );
    }

    if (genderFilter && genderFilter !== '') {
      filtered = filtered.filter(emp => emp.gender === genderFilter);
    }

    if (administrativeUnitFilter.trim()) {
      filtered = filtered.filter(emp =>
        emp.recycledCircle && emp.recycledCircle.toLowerCase().includes(administrativeUnitFilter.toLowerCase())
      );
    }

    setFilteredEmployees(filtered);
    return filtered;
  };

  const handlePreview = () => {
    const filtered = applyFilters();
    if (filtered.length === 0) {
      alert('⚠️ لا توجد نتائج للمعاينة!\nتأكد من صحة معايير التصفية.');
      return;
    }
    setShowPreview(true);
  };

  const exportFilteredData = () => {
    if (filteredEmployees.length === 0) {
      alert('⚠️ لا توجد بيانات مفلترة للتصدير!');
      return;
    }

    try {
      // إنشاء البيانات مع ترتيب RTL احترافي
      const exportData = createRTLExcelData(filteredEmployees);

      // إنشاء معلومات التصفية للـ Header
      const filterInfo = [];
      if (specializationFilter) filterInfo.push(`التخصص: ${specializationFilter}`);
      if (departmentFilter) filterInfo.push(`الدائرة: ${departmentFilter}`);
      const filterText = filterInfo.length > 0 ?
        `تقرير مفلتر - ${filterInfo.join(' | ')}` :
        'تقرير شامل لجميع الموظفين';

      // إنشاء ورقة Excel احترافية مع Header وجدول
      const ws = createProfessionalExcelSheet(exportData, 'البيانات المفلترة', filterText);

      const wb = XLSX.utils.book_new();

      // إعداد خصائص الكتاب
      wb.Props = {
        Title: "تقرير التصفية - محافظة الديوانية",
        Subject: "بيانات عقود الموظفين المفلترة",
        Author: "المبرمج المحاسب: علي عاجل خشان المحنة",
        CreatedDate: new Date(),
        Company: "محافظة الديوانية"
      };

      // إعداد اتجاه RTL للكتاب
      wb.Workbook = {
        Views: [{
          RTL: true
        }]
      };

      XLSX.utils.book_append_sheet(wb, ws, 'البيانات المفلترة');

      // إنشاء اسم ملف ذكي
      const fileFilterInfo = [];
      if (specializationFilter) fileFilterInfo.push(`التخصص_${specializationFilter}`);
      if (departmentFilter) fileFilterInfo.push(`الدائرة_${departmentFilter}`);
      const fileFilterText = fileFilterInfo.length > 0 ? fileFilterInfo.join('_') : 'جميع_الموظفين';

      const fileName = `تقرير_التصفية_${fileFilterText}_${new Date().toLocaleDateString('ar-SA').replace(/\//g, '-')}.xlsx`;
      XLSX.writeFile(wb, fileName);

      alert('✅ تم تصدير البيانات بنجاح!');
    } catch (error) {
      console.error('خطأ في التصدير:', error);
      alert('❌ حدث خطأ في تصدير البيانات');
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0,0,0,0.8)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 10000
    }}>
      <div style={{
        background: 'white',
        borderRadius: '20px',
        padding: '30px',
        maxWidth: '800px',
        width: '95%',
        maxHeight: '90vh',
        overflowY: 'auto',
        boxShadow: '0 20px 40px rgba(0,0,0,0.3)'
      }}>
        <h3 style={{ textAlign: 'center', marginBottom: '25px', color: '#667eea' }}>
          🔍 إعداد التصفية العامة
        </h3>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '20px', marginBottom: '25px' }}>
          <div>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#495057' }}>
              🎓 التصفية حسب التخصص الدقيق:
            </label>
            <input
              type="text"
              value={specializationFilter}
              onChange={(e) => setSpecializationFilter(e.target.value)}
              placeholder="مثال: هندسة حاسبات"
              style={{
                width: '100%',
                padding: '12px',
                border: '2px solid #ddd',
                borderRadius: '8px',
                fontSize: '16px'
              }}
            />
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#495057' }}>
              🏢 التصفية حسب الدائرة التي تم المباشرة بها:
            </label>
            <input
              type="text"
              value={departmentFilter}
              onChange={(e) => setDepartmentFilter(e.target.value)}
              placeholder="مثال: وزارة التربية"
              style={{
                width: '100%',
                padding: '12px',
                border: '2px solid #ddd',
                borderRadius: '8px',
                fontSize: '16px'
              }}
            />
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#495057' }}>
              👤 التصفية حسب الجنس:
            </label>
            <select
              value={genderFilter}
              onChange={(e) => setGenderFilter(e.target.value)}
              style={{
                width: '100%',
                padding: '12px',
                border: '2px solid #ddd',
                borderRadius: '8px',
                fontSize: '16px'
              }}
            >
              <option value="">الكل</option>
              <option value="male">ذكر</option>
              <option value="female">أنثى</option>
            </select>
          </div>

          <div>
            <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold', color: '#495057' }}>
              🏛️ التصفية حسب الوحدات الإدارية:
            </label>
            <input
              type="text"
              value={administrativeUnitFilter}
              onChange={(e) => setAdministrativeUnitFilter(e.target.value)}
              placeholder="مثال: قضاء الديوانية"
              style={{
                width: '100%',
                padding: '12px',
                border: '2px solid #ddd',
                borderRadius: '8px',
                fontSize: '16px'
              }}
            />
          </div>

          <div style={{ display: 'flex', gap: '10px', justifyContent: 'center', flexWrap: 'wrap' }}>
            <button
              onClick={applyFilters}
              style={{
                padding: '12px 20px',
                background: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontWeight: 'bold',
                fontSize: '14px'
              }}
            >
              🔍 تطبيق التصفية
            </button>
            <button
              onClick={handlePreview}
              style={{
                padding: '12px 20px',
                background: '#17a2b8',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontWeight: 'bold',
                fontSize: '14px'
              }}
            >
              👁️ معاينة التصفية
            </button>
          </div>
        </div>

        {filteredEmployees.length > 0 && (
          <div style={{ marginBottom: '20px' }}>
            <h4 style={{ color: '#28a745', marginBottom: '15px' }}>
              ✅ نتائج التصفية ({convertToArabicNumbers(filteredEmployees.length)} موظف):
            </h4>
            <div style={{ maxHeight: '300px', overflowY: 'auto', border: '1px solid #ddd', borderRadius: '8px' }}>
              <table style={{ width: '100%', borderCollapse: 'collapse', fontSize: '0.9rem' }}>
                <thead>
                  <tr style={{ background: '#f8f9fa' }}>
                    <th style={{ padding: '10px', border: '1px solid #ddd', textAlign: 'center' }}>الاسم الكامل</th>
                    <th style={{ padding: '10px', border: '1px solid #ddd', textAlign: 'center' }}>التخصص</th>
                    <th style={{ padding: '10px', border: '1px solid #ddd', textAlign: 'center' }}>الدائرة</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredEmployees.map((emp, index) => (
                    <tr key={index}>
                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'center' }}>{emp.fullName}</td>
                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'center' }}>{emp.specialization}</td>
                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'center' }}>{emp.recycledCircle}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <button
            onClick={onClose}
            style={{
              padding: '12px 30px',
              background: '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              cursor: 'pointer',
              fontWeight: 'bold'
            }}
          >
            إغلاق
          </button>
        </div>
      </div>

      {/* مكون المعاينة */}
      {showPreview && (
        <FilterPreviewModal
          employees={filteredEmployees}
          specializationFilter={specializationFilter}
          departmentFilter={departmentFilter}
          onClose={() => setShowPreview(false)}
        />
      )}
    </div>
  );
}

// Filter Preview Modal Component
function FilterPreviewModal({ employees, specializationFilter, departmentFilter, onClose }) {
  const printReport = () => {
    const printWindow = window.open('', '_blank');
    const currentDate = new Date().toLocaleDateString('ar-SA');
    const currentTime = new Date().toLocaleTimeString('ar-SA');

    const filterInfo = [];
    if (specializationFilter) filterInfo.push(`التخصص: ${specializationFilter}`);
    if (departmentFilter) filterInfo.push(`الدائرة: ${departmentFilter}`);
    const filterText = filterInfo.length > 0 ? filterInfo.join(' | ') : 'جميع الموظفين';

    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <title>تقرير التصفية - ${filterText}</title>
        <style>
          body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
            color: #333;
            direction: rtl;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #4472C4;
            padding-bottom: 20px;
          }
          .main-title {
            font-size: 28px;
            font-weight: bold;
            color: #4472C4;
            margin: 0 0 10px 0;
          }
          .sub-title {
            font-size: 18px;
            color: #666;
            margin: 5px 0;
          }
          .filter-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #4472C4;
          }
          .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
          }
          .stat-item {
            text-align: center;
          }
          .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
          }
          .stat-label {
            font-size: 14px;
            color: #666;
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
          }
          th {
            background: linear-gradient(135deg, #4472C4, #2c5aa0);
            color: white;
            padding: 15px 8px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #ddd;
            font-size: 14px;
          }
          td {
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #ddd;
            font-size: 13px;
          }
          tr:nth-child(even) {
            background-color: #f8f9fa;
          }
          tr:hover {
            background-color: #e3f2fd;
          }
          .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
          }
          .developer {
            color: #4472C4;
            font-weight: bold;
          }
          @media print {
            body { margin: 0; }
            .no-print { display: none; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1 class="main-title">تقرير التصفية - نظام إدخال بيانات العقود</h1>
          <p class="sub-title">محافظة الديوانية</p>
          <p class="sub-title">التاريخ: ${currentDate} | الوقت: ${currentTime}</p>
        </div>

        <div class="filter-info">
          <h3 style="margin: 0 0 10px 0; color: #4472C4;">معايير التصفية:</h3>
          <p style="margin: 0; font-size: 16px; font-weight: bold;">${filterText}</p>
        </div>

        <div class="stats">
          <div class="stat-item">
            <div class="stat-number">${employees.length}</div>
            <div class="stat-label">إجمالي النتائج</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">${new Set(employees.map(emp => emp.specialization)).size}</div>
            <div class="stat-label">التخصصات المختلفة</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">${new Set(employees.map(emp => emp.recycledCircle)).size}</div>
            <div class="stat-label">الدوائر المختلفة</div>
          </div>
        </div>

        <table>
          <thead>
            <tr>
              <th style="width: 5%;">#</th>
              <th style="width: 20%;">الاسم الكامل</th>
              <th style="width: 15%;">اسم الأم</th>
              <th style="width: 10%;">تاريخ الميلاد</th>
              <th style="width: 12%;">الرقم الوطني</th>
              <th style="width: 10%;">رقم الهاتف</th>
              <th style="width: 18%;">التخصص الدقيق</th>
              <th style="width: 10%;">الدائرة</th>
            </tr>
          </thead>
          <tbody>
            ${employees.map((emp, index) => `
              <tr>
                <td>${convertToArabicNumbers(index + 1)}</td>
                <td>${emp.fullName || '-'}</td>
                <td>${emp.motherName || '-'}</td>
                <td>${emp.birthDate ? convertToArabicNumbers(new Date(emp.birthDate).toLocaleDateString('ar-SA')) : '-'}</td>
                <td>${convertToArabicNumbers(emp.nationalId) || '-'}</td>
                <td>${convertToArabicNumbers(emp.phoneNumber) || '-'}</td>
                <td>${emp.specialization || '-'}</td>
                <td>${emp.recycledCircle || '-'}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <div class="footer">
          <p>تم إنشاء هذا التقرير بواسطة نظام إدخال بيانات العقود</p>
          <p>تطوير: <span class="developer">علي عاجل خشان المحنة</span> | محافظة الديوانية</p>
          <p>تاريخ الطباعة: ${currentDate} - ${currentTime}</p>
        </div>
      </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    setTimeout(() => {
      printWindow.print();
    }, 500);
  };

  const exportToExcel = () => {
    if (employees.length === 0) {
      alert('⚠️ لا توجد بيانات للتصدير!');
      return;
    }

    try {
      // إنشاء البيانات مع ترتيب RTL احترافي
      const exportData = createRTLExcelData(employees);

      // إنشاء معلومات التصفية للـ Header
      const filterInfo = [];
      if (specializationFilter) filterInfo.push(`التخصص: ${specializationFilter}`);
      if (departmentFilter) filterInfo.push(`الدائرة: ${departmentFilter}`);
      const filterText = filterInfo.length > 0 ?
        `تقرير التصفية المتقدم - ${filterInfo.join(' | ')}` :
        'تقرير شامل لجميع الموظفين';

      // إنشاء ورقة Excel احترافية مع Header وجدول
      const ws = createProfessionalExcelSheet(exportData, 'البيانات المفلترة', filterText);

      const wb = XLSX.utils.book_new();

      // إعداد خصائص الكتاب
      wb.Props = {
        Title: "تقرير التصفية المتقدم - محافظة الديوانية",
        Subject: "بيانات عقود الموظفين المفلترة - معاينة شاملة",
        Author: "المبرمج المحاسب: علي عاجل خشان المحنة",
        CreatedDate: new Date(),
        Company: "محافظة الديوانية - قسم تقنية المعلومات"
      };

      // إعداد اتجاه RTL للكتاب
      wb.Workbook = {
        Views: [{
          RTL: true
        }]
      };

      XLSX.utils.book_append_sheet(wb, ws, 'البيانات المفلترة');

      const fileFilterInfo2 = [];
      if (specializationFilter) fileFilterInfo2.push(`التخصص_${specializationFilter}`);
      if (departmentFilter) fileFilterInfo2.push(`الدائرة_${departmentFilter}`);
      const fileFilterText2 = fileFilterInfo2.length > 0 ? fileFilterInfo2.join('_') : 'جميع_الموظفين';

      const fileName = `تقرير_التصفية_المتقدم_${fileFilterText2}_${new Date().toLocaleDateString('ar-SA').replace(/\//g, '-')}.xlsx`;
      XLSX.writeFile(wb, fileName);

      alert('✅ تم تصدير البيانات بنجاح!\n📊 ملف Excel احترافي جاهز للاستخدام');
    } catch (error) {
      console.error('خطأ في التصدير:', error);
      alert('❌ حدث خطأ في تصدير البيانات');
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0,0,0,0.9)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 20000
    }}>
      <div style={{
        background: 'white',
        borderRadius: '15px',
        width: '95%',
        height: '95%',
        display: 'flex',
        flexDirection: 'column',
        boxShadow: '0 25px 50px rgba(0,0,0,0.5)'
      }}>
        {/* Header */}
        <div style={{
          background: 'linear-gradient(135deg, #4472C4, #2c5aa0)',
          color: 'white',
          padding: '20px',
          borderRadius: '15px 15px 0 0',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div>
            <h2 style={{ margin: '0 0 5px 0', fontSize: '24px' }}>📊 معاينة تقرير التصفية</h2>
            <p style={{ margin: 0, opacity: 0.9 }}>
              {specializationFilter && `التخصص: ${specializationFilter}`}
              {specializationFilter && departmentFilter && ' | '}
              {departmentFilter && `الدائرة: ${departmentFilter}`}
              {!specializationFilter && !departmentFilter && 'جميع الموظفين'}
            </p>
          </div>
          <div style={{ display: 'flex', gap: '10px' }}>
            <button
              onClick={printReport}
              style={{
                padding: '10px 20px',
                background: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontWeight: 'bold'
              }}
            >
              🖨️ طباعة
            </button>
            <button
              onClick={exportToExcel}
              style={{
                padding: '10px 20px',
                background: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontWeight: 'bold'
              }}
            >
              📊 تصدير Excel
            </button>
            <button
              onClick={onClose}
              style={{
                padding: '10px 20px',
                background: '#dc3545',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontWeight: 'bold'
              }}
            >
              ✕ إغلاق
            </button>
          </div>
        </div>

        {/* Stats */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-around',
          padding: '20px',
          background: '#f8f9fa',
          borderBottom: '1px solid #ddd'
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#4472C4' }}>{convertToArabicNumbers(employees.length)}</div>
            <div style={{ fontSize: '14px', color: '#666' }}>إجمالي النتائج</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#28a745' }}>
              {convertToArabicNumbers(new Set(employees.map(emp => emp.specialization)).size)}
            </div>
            <div style={{ fontSize: '14px', color: '#666' }}>التخصصات المختلفة</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#17a2b8' }}>
              {convertToArabicNumbers(new Set(employees.map(emp => emp.recycledCircle)).size)}
            </div>
            <div style={{ fontSize: '14px', color: '#666' }}>الدوائر المختلفة</div>
          </div>
        </div>

        {/* Table */}
        <div style={{ flex: 1, overflow: 'auto', padding: '20px' }}>
          <table style={{
            width: '100%',
            borderCollapse: 'collapse',
            background: 'white',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
          }}>
            <thead>
              <tr style={{ background: 'linear-gradient(135deg, #4472C4, #2c5aa0)' }}>
                <th style={{ padding: '15px 8px', color: 'white', border: '1px solid #ddd', textAlign: 'center', width: '5%' }}>#</th>
                <th style={{ padding: '15px 8px', color: 'white', border: '1px solid #ddd', textAlign: 'center', width: '18%' }}>الاسم الكامل</th>
                <th style={{ padding: '15px 8px', color: 'white', border: '1px solid #ddd', textAlign: 'center', width: '15%' }}>اسم الأم</th>
                <th style={{ padding: '15px 8px', color: 'white', border: '1px solid #ddd', textAlign: 'center', width: '10%' }}>تاريخ الميلاد</th>
                <th style={{ padding: '15px 8px', color: 'white', border: '1px solid #ddd', textAlign: 'center', width: '12%' }}>الرقم الوطني</th>
                <th style={{ padding: '15px 8px', color: 'white', border: '1px solid #ddd', textAlign: 'center', width: '10%' }}>رقم الهاتف</th>
                <th style={{ padding: '15px 8px', color: 'white', border: '1px solid #ddd', textAlign: 'center', width: '20%' }}>التخصص الدقيق</th>
                <th style={{ padding: '15px 8px', color: 'white', border: '1px solid #ddd', textAlign: 'center', width: '10%' }}>الدائرة</th>
              </tr>
            </thead>
            <tbody>
              {employees.map((emp, index) => (
                <tr key={index} style={{
                  backgroundColor: index % 2 === 0 ? '#f8f9fa' : 'white',
                  transition: 'background-color 0.2s'
                }}
                onMouseEnter={(e) => e.target.parentElement.style.backgroundColor = '#e3f2fd'}
                onMouseLeave={(e) => e.target.parentElement.style.backgroundColor = index % 2 === 0 ? '#f8f9fa' : 'white'}
                >
                  <td style={{ padding: '12px 8px', border: '1px solid #ddd', textAlign: 'center', fontWeight: 'bold' }}>{convertToArabicNumbers(index + 1)}</td>
                  <td style={{ padding: '12px 8px', border: '1px solid #ddd', textAlign: 'center' }}>{emp.fullName || '-'}</td>
                  <td style={{ padding: '12px 8px', border: '1px solid #ddd', textAlign: 'center' }}>{emp.motherName || '-'}</td>
                  <td style={{ padding: '12px 8px', border: '1px solid #ddd', textAlign: 'center' }}>
                    {emp.birthDate ? convertToArabicNumbers(new Date(emp.birthDate).toLocaleDateString('ar-SA')) : '-'}
                  </td>
                  <td style={{ padding: '12px 8px', border: '1px solid #ddd', textAlign: 'center' }}>{convertToArabicNumbers(emp.nationalId) || '-'}</td>
                  <td style={{ padding: '12px 8px', border: '1px solid #ddd', textAlign: 'center' }}>{convertToArabicNumbers(emp.phoneNumber) || '-'}</td>
                  <td style={{ padding: '12px 8px', border: '1px solid #ddd', textAlign: 'center' }}>{emp.specialization || '-'}</td>
                  <td style={{ padding: '12px 8px', border: '1px solid #ddd', textAlign: 'center' }}>{emp.recycledCircle || '-'}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

// Properties Modal Component
function PropertiesModal({ onClose, deviceRestriction, toggleDeviceRestriction, clearDeviceIds }) {
  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0,0,0,0.8)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 10000
    }}>
      <div style={{
        background: 'white',
        borderRadius: '20px',
        padding: '30px',
        maxWidth: '500px',
        width: '90%',
        boxShadow: '0 20px 40px rgba(0,0,0,0.3)',
        maxHeight: '80vh',
        overflowY: 'auto'
      }}>
        <h3 style={{ textAlign: 'center', marginBottom: '25px', color: '#667eea' }}>
          🛠️ تغيير خواص النظام
        </h3>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
          {/* الخيار الأول: قيد الإدخال */}
          <div style={{
            border: '2px solid #e9ecef',
            borderRadius: '12px',
            padding: '20px',
            background: deviceRestriction ? '#d4edda' : '#f8d7da'
          }}>
            <h4 style={{ margin: '0 0 10px 0', color: '#495057' }}>
              {deviceRestriction ? '🔒' : '🔓'} قيد الإدخال للأجهزة
            </h4>
            <p style={{ margin: '0 0 15px 0', color: '#6c757d', fontSize: '0.9rem' }}>
              {deviceRestriction ?
                'مفعل: كل جهاز يمكنه الإدخال مرة واحدة فقط' :
                'معطل: يمكن للأجهزة الإدخال أكثر من مرة'
              }
            </p>
            <button
              onClick={toggleDeviceRestriction}
              style={{
                padding: '10px 20px',
                background: deviceRestriction ? '#dc3545' : '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontWeight: 'bold'
              }}
            >
              {deviceRestriction ? '🔓 إلغاء القيد' : '🔒 تفعيل القيد'}
            </button>
          </div>

          {/* الخيار الثالث: مسح معرفات الأجهزة */}
          <div style={{
            border: '2px solid #ffc107',
            borderRadius: '12px',
            padding: '20px',
            background: '#fff3cd'
          }}>
            <h4 style={{ margin: '0 0 10px 0', color: '#856404' }}>
              🗑️ مسح معرفات الأجهزة
            </h4>
            <p style={{ margin: '0 0 15px 0', color: '#856404', fontSize: '0.9rem' }}>
              مسح جميع معرفات الأجهزة المحفوظة للسماح لها بالإدخال مرة أخرى
            </p>
            <button
              onClick={clearDeviceIds}
              style={{
                padding: '10px 20px',
                background: '#ffc107',
                color: '#212529',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontWeight: 'bold'
              }}
            >
              🗑️ مسح البيانات
            </button>
          </div>
        </div>

        <div style={{ display: 'flex', justifyContent: 'center', marginTop: '25px' }}>
          <button
            onClick={onClose}
            style={{
              padding: '12px 30px',
              background: '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              cursor: 'pointer',
              fontWeight: 'bold'
            }}
          >
            إغلاق
          </button>
        </div>
      </div>
    </div>
  );
}

// Security Modal Component
function SecurityModal({ onClose }) {
  const [deletePassword, setDeletePassword] = useState(localStorage.getItem('deletePassword') || 'a1234a1234A@#1');
  const [filterPassword, setFilterPassword] = useState(localStorage.getItem('filterPassword') || '1000');

  const saveChanges = () => {
    if (!deletePassword.trim() || !filterPassword.trim()) {
      alert('❌ يجب ملء جميع الحقول!');
      return;
    }

    localStorage.setItem('deletePassword', deletePassword);
    localStorage.setItem('filterPassword', filterPassword);

    alert('✅ تم حفظ التغييرات بنجاح!\nكلمات المرور الجديدة أصبحت فعالة.');
    onClose();
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0,0,0,0.8)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 10000
    }}>
      <div style={{
        background: 'white',
        borderRadius: '20px',
        padding: '30px',
        maxWidth: '500px',
        width: '90%',
        boxShadow: '0 20px 40px rgba(0,0,0,0.3)'
      }}>
        <h3 style={{ textAlign: 'center', marginBottom: '25px', color: '#dc3545' }}>
          🔒 إعدادات الأمان
        </h3>

        <div style={{ display: 'flex', flexDirection: 'column', gap: '25px' }}>
          {/* تغيير كلمة مرور الحذف */}
          <div style={{
            border: '2px solid #dc3545',
            borderRadius: '12px',
            padding: '20px',
            background: '#f8d7da'
          }}>
            <h4 style={{ margin: '0 0 15px 0', color: '#721c24' }}>
              🗑️ كلمة مرور الحذف والخواص
            </h4>
            <p style={{ margin: '0 0 15px 0', color: '#721c24', fontSize: '0.9rem' }}>
              كلمة المرور المستخدمة لحذف القيود وتغيير الخواص
            </p>
            <input
              type="password"
              value={deletePassword}
              onChange={(e) => setDeletePassword(e.target.value)}
              placeholder="أدخل كلمة المرور الجديدة"
              style={{
                width: '100%',
                padding: '12px',
                border: '2px solid #dc3545',
                borderRadius: '8px',
                fontSize: '16px',
                fontFamily: 'monospace',
                textAlign: 'center'
              }}
            />
          </div>

          {/* تغيير كلمة مرور التصفية */}
          <div style={{
            border: '2px solid #007bff',
            borderRadius: '12px',
            padding: '20px',
            background: '#d1ecf1'
          }}>
            <h4 style={{ margin: '0 0 15px 0', color: '#0c5460' }}>
              🔍 كلمة مرور التصفية
            </h4>
            <p style={{ margin: '0 0 15px 0', color: '#0c5460', fontSize: '0.9rem' }}>
              كلمة المرور المستخدمة للوصول لإعدادات التصفية
            </p>
            <input
              type="password"
              value={filterPassword}
              onChange={(e) => setFilterPassword(e.target.value)}
              placeholder="أدخل كلمة المرور الجديدة"
              style={{
                width: '100%',
                padding: '12px',
                border: '2px solid #007bff',
                borderRadius: '8px',
                fontSize: '16px',
                fontFamily: 'monospace',
                textAlign: 'center'
              }}
            />
          </div>
        </div>

        <div style={{ display: 'flex', gap: '15px', justifyContent: 'center', marginTop: '25px' }}>
          <button
            onClick={saveChanges}
            style={{
              padding: '12px 25px',
              background: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontWeight: 'bold',
              fontSize: '16px'
            }}
          >
            💾 حفظ التغييرات
          </button>
          <button
            onClick={onClose}
            style={{
              padding: '12px 25px',
              background: '#6c757d',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontWeight: 'bold'
            }}
          >
            إلغاء
          </button>
        </div>
      </div>
    </div>
  );
}

// Settings Component
function SettingsPage({ onNavigate }) {
  const [showPropertiesModal, setShowPropertiesModal] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showSecurityModal, setShowSecurityModal] = useState(false);
  const [showSecurityPasswordModal, setShowSecurityPasswordModal] = useState(false);
  const [deviceRestriction, setDeviceRestriction] = useState(
    localStorage.getItem('deviceRestriction') !== 'false'
  );
  const [contextMenuDisabled, setContextMenuDisabled] = useState(
    localStorage.getItem('contextMenuDisabled') !== 'false'
  );

  const handlePasswordSubmit = (password) => {
    const savedDeletePassword = localStorage.getItem('deletePassword') || 'a1234a1234A@#1';
    if (password === savedDeletePassword) {
      setShowPasswordModal(false);
      setShowPropertiesModal(true);
    } else {
      alert('❌ كلمة المرور غير صحيحة!');
    }
  };

  const handleSecurityPasswordSubmit = (password) => {
    const savedDeletePassword = localStorage.getItem('deletePassword') || 'a1234a1234A@#1';
    if (password === savedDeletePassword) {
      setShowSecurityPasswordModal(false);
      setShowSecurityModal(true);
    } else {
      alert('❌ كلمة المرور غير صحيحة!');
    }
  };

  const toggleDeviceRestriction = () => {
    const newValue = !deviceRestriction;
    setDeviceRestriction(newValue);
    localStorage.setItem('deviceRestriction', newValue.toString());
    alert(newValue ?
      '🔒 تم تفعيل قيد الإدخال لمرة واحدة فقط لكل جهاز' :
      '🔓 تم إلغاء قيد الإدخال - يمكن الإدخال أكثر من مرة'
    );
  };

  const clearDeviceIds = () => {
    const confirm = window.confirm('⚠️ تأكيد مسح بيانات الأجهزة\n\nهل أنت متأكد من مسح جميع معرفات الأجهزة؟\nسيتمكن جميع الأجهزة من الإدخال مرة أخرى.');

    if (confirm) {
      localStorage.removeItem('submittedDevices');
      alert('✅ تم مسح جميع معرفات الأجهزة بنجاح!\nيمكن لجميع الأجهزة الإدخال مرة أخرى.');
    }
  };

  const toggleContextMenu = () => {
    const newValue = !contextMenuDisabled;
    setContextMenuDisabled(newValue);
    localStorage.setItem('contextMenuDisabled', newValue.toString());

    // تطبيق التغيير فوراً
    if (newValue) {
      document.body.classList.add('disable-context-menu');
      document.addEventListener('contextmenu', preventContextMenu);
    } else {
      document.body.classList.remove('disable-context-menu');
      document.removeEventListener('contextmenu', preventContextMenu);
    }

    alert(newValue ?
      '🔒 تم إغلاق القائمة اليمنى (منع النقر بالزر الأيمن)' :
      '🔓 تم فتح القائمة اليمنى (السماح بالنقر بالزر الأيمن)'
    );
  };

  const preventContextMenu = (e) => {
    e.preventDefault();
    return false;
  };

  return (
    <div className="container" style={{ marginBottom: '120px' }}>
      <div className="header">
        <h1>⚙️ الإعدادات</h1>
        <button className="btn btn-secondary" onClick={() => onNavigate('dashboard')}>
          العودة للرئيسية
        </button>
      </div>

      <div className="form-container">
        <h3>معلومات المستخدم</h3>
        <div className="form-group">
          <label>اسم المستخدم</label>
          <input type="text" value="علي عاجل خشان المحنة" disabled />
        </div>
        <div className="form-group">
          <label>المحافظة</label>
          <input type="text" value="محافظة الديوانية" disabled />
        </div>
        <div className="form-group">
          <label>الدور</label>
          <input type="text" value="مدير النظام" disabled />
        </div>
      </div>

      {/* مكون تغيير الخواص */}
      <div className="card" style={{ marginTop: '20px' }}>
        <div style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          borderRadius: '15px',
          padding: '20px',
          margin: '20px 0',
          color: 'white',
          cursor: 'pointer',
          transition: 'transform 0.3s ease',
          boxShadow: '0 10px 25px rgba(102, 126, 234, 0.3)'
        }}
        onClick={() => setShowPasswordModal(true)}
        onMouseEnter={(e) => e.target.style.transform = 'translateY(-3px)'}
        onMouseLeave={(e) => e.target.style.transform = 'translateY(0)'}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <div style={{ fontSize: '2rem' }}>🛠️</div>
            <div>
              <h4 style={{ margin: '0 0 5px 0', color: 'white' }}>تغيير الخواص</h4>
              <p style={{ margin: 0, opacity: 0.9, fontSize: '0.9rem' }}>
                إدارة قيود الإدخال ومعرفات الأجهزة
              </p>
            </div>
            <div style={{ marginLeft: 'auto', fontSize: '1.5rem' }}>🔒</div>
          </div>
        </div>

        {/* مكون الأمان */}
        <div style={{
          background: 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)',
          borderRadius: '15px',
          padding: '20px',
          margin: '20px 0',
          color: 'white',
          cursor: 'pointer',
          transition: 'transform 0.3s ease',
          boxShadow: '0 10px 25px rgba(220, 53, 69, 0.4)'
        }}
        onClick={() => setShowSecurityPasswordModal(true)}
        onMouseEnter={(e) => e.target.style.transform = 'translateY(-3px)'}
        onMouseLeave={(e) => e.target.style.transform = 'translateY(0)'}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <div style={{ fontSize: '2rem' }}>🔒</div>
            <div>
              <h4 style={{ margin: '0 0 5px 0', color: 'white' }}>الأمان</h4>
              <p style={{ margin: 0, opacity: 0.9, fontSize: '0.9rem' }}>
                تغيير كلمات المرور وإعدادات الأمان
              </p>
            </div>
            <div style={{ marginLeft: 'auto', fontSize: '1.5rem' }}>⚠️</div>
          </div>
        </div>

        {/* تحكم القائمة اليمنى */}
        <div style={{
          background: 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)',
          borderRadius: '15px',
          padding: '20px',
          margin: '20px 0',
          color: 'white',
          cursor: 'pointer',
          transition: 'transform 0.3s ease',
          boxShadow: '0 10px 25px rgba(23, 162, 184, 0.3)'
        }}
        onClick={toggleContextMenu}
        onMouseEnter={(e) => e.target.style.transform = 'translateY(-3px)'}
        onMouseLeave={(e) => e.target.style.transform = 'translateY(0)'}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <div style={{ fontSize: '2rem' }}>{contextMenuDisabled ? '🔒' : '🔓'}</div>
            <div>
              <h4 style={{ margin: '0 0 5px 0', color: 'white' }}>
                {contextMenuDisabled ? 'إغلاق القائمة اليمنى' : 'فتح القائمة اليمنى'}
              </h4>
              <p style={{ margin: 0, opacity: 0.9, fontSize: '0.9rem' }}>
                {contextMenuDisabled ?
                  'القائمة اليمنى مغلقة - انقر للفتح' :
                  'القائمة اليمنى مفتوحة - انقر للإغلاق'
                }
              </p>
            </div>
            <div style={{ marginLeft: 'auto', fontSize: '1.5rem' }}>
              {contextMenuDisabled ? '🚫' : '✅'}
            </div>
          </div>
        </div>

        <div style={{ marginTop: '20px' }}>
          <h4>📊 معلومات النظام:</h4>
          <ul>
            <li>إصدار النظام: 2.0.1</li>
            <li>تاريخ آخر تحديث: {new Date().toLocaleDateString('en-US')}</li>
            <li>حالة قاعدة البيانات: متصلة</li>
            <li>قيد الإدخال: {deviceRestriction ? '🔒 مفعل' : '🔓 معطل'}</li>
            <li>القائمة اليمنى: {contextMenuDisabled ? '🔒 مغلقة' : '🔓 مفتوحة'}</li>
          </ul>
        </div>
      </div>

      {/* مودال كلمة المرور */}
      <PasswordModal
        isOpen={showPasswordModal}
        onClose={() => setShowPasswordModal(false)}
        onSubmit={handlePasswordSubmit}
        title="الوصول للخواص المتقدمة"
        message="أدخل كلمة المرور للوصول لإعدادات تغيير الخواص"
      />

      {/* مودال كلمة مرور الأمان */}
      <PasswordModal
        isOpen={showSecurityPasswordModal}
        onClose={() => setShowSecurityPasswordModal(false)}
        onSubmit={handleSecurityPasswordSubmit}
        title="الوصول لإعدادات الأمان"
        message="أدخل كلمة المرور للوصول لإعدادات الأمان"
      />

      {/* مودال الخواص */}
      {showPropertiesModal && (
        <PropertiesModal
          onClose={() => setShowPropertiesModal(false)}
          deviceRestriction={deviceRestriction}
          toggleDeviceRestriction={toggleDeviceRestriction}
          clearDeviceIds={clearDeviceIds}
        />
      )}

      {/* مودال الأمان */}
      {showSecurityModal && (
        <SecurityModal
          onClose={() => setShowSecurityModal(false)}
        />
      )}
    </div>
  );
}

// Bottom Navigation
function BottomNav({ currentPage, onNavigate }) {
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState(null);

  const navItems = [
    { id: 'dashboard', label: 'الرئيسية', icon: '🏠' },
    { id: 'data-entry', label: 'البيانات', icon: '📋' },
    { id: 'reports', label: 'التقارير', icon: '📊', protected: true },
    { id: 'employees', label: 'الموظفين', icon: '👥', protected: true },
    { id: 'filter', label: 'التصفية', icon: '🔍', protected: true },
    { id: 'settings', label: 'الإعدادات', icon: '⚙️', protected: true }
  ];

  const handleNavigation = (item) => {
    if (item.protected) {
      setPendingNavigation(item.id);
      setShowPasswordModal(true);
    } else {
      onNavigate(item.id);
    }
  };

  const handlePasswordSubmit = (password) => {
    if (password === '1000') {
      setShowPasswordModal(false);
      if (pendingNavigation) {
        onNavigate(pendingNavigation);
        setPendingNavigation(null);
      }
    } else {
      alert('❌ كلمة المرور غير صحيحة!');
    }
  };

  const handlePasswordClose = () => {
    setShowPasswordModal(false);
    setPendingNavigation(null);
  };

  return (
    <>
      <nav className="bottom-nav">
        {navItems.map(item => (
          <div
            key={item.id}
            className={`nav-item ${currentPage === item.id ? 'active' : ''}`}
            data-page={item.id}
            onClick={() => handleNavigation(item)}
          >
            <div className="icon">{item.icon}</div>
            <span>{item.label}</span>
          </div>
        ))}
      </nav>

      <PasswordModal
        isOpen={showPasswordModal}
        onClose={handlePasswordClose}
        onSubmit={handlePasswordSubmit}
        title="منطقة محمية"
        message="هذه الواجهة محمية بكلمة مرور. يرجى إدخال كلمة المرور للمتابعة."
      />
    </>
  );
}

export default App;
