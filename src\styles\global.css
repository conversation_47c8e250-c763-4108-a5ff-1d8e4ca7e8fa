* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Arial', sans-serif;
  background: linear-gradient(135deg, #87ceeb 0%, #ffc0cb 30%, #dda0dd 60%, #98fb98 100%);
  background-size: 400% 400%;
  animation: backgroundShift 8s ease-in-out infinite;
  min-height: 100vh;
  direction: rtl;
  color: #2c3e50;
  /* منع القائمة اليمنى */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* أنيميشن الخلفية */
@keyframes backgroundShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* منع القائمة اليمنى */
body.disable-context-menu {
  pointer-events: auto;
}

body.disable-context-menu * {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

#root {
  min-height: 100vh;
}

/* الانترو المتحرك الجميل */
.intro-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 25%, #45b7d1 50%, #96ceb4 75%, #feca57 100%);
  background-size: 500% 500%;
  animation: introBackground 6s ease-in-out infinite;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  overflow: hidden;
}

.intro-content {
  text-align: center;
  max-width: 900px;
  padding: 40px;
}

.intro-line {
  opacity: 0;
  transform: translateY(50px) scale(0.8);
  animation: introSlideIn 0.8s ease-out forwards;
  margin: 20px 0;
  text-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.intro-line-1 {
  font-size: 4rem;
  font-weight: 900;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: introSlideIn 0.8s ease-out forwards, textGlow 3s ease-in-out infinite;
  animation-delay: 0s, 0.5s;
  text-shadow: 0 0 30px rgba(255,255,255,0.5);
}

.intro-line-2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  animation-delay: 0.6s;
  text-shadow: 0 2px 10px rgba(44, 62, 80, 0.3);
}

.intro-line-3 {
  font-size: 3rem;
  font-weight: 800;
  color: #34495e;
  animation-delay: 1.2s;
  text-shadow: 0 3px 15px rgba(52, 73, 94, 0.4);
}

.intro-manager-name {
  font-size: 4.5rem;
  font-weight: 900;
  background: linear-gradient(45deg, #f39c12, #e74c3c, #9b59b6, #3498db, #2ecc71);
  background-size: 500% 500%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: introSlideIn 0.8s ease-out forwards, managerGlow 4s ease-in-out infinite, gentleFloat 5s ease-in-out infinite;
  animation-delay: 1.8s, 2.1s, 2.3s;
  text-shadow: 0 0 40px rgba(243, 156, 18, 0.6);
  filter: drop-shadow(0 5px 15px rgba(0,0,0,0.3));
}

.intro-line-5 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  animation-delay: 2.4s;
  text-shadow: 0 2px 10px rgba(44, 62, 80, 0.3);
}

.intro-developer-name {
  font-size: 4.5rem;
  font-weight: 900;
  background: linear-gradient(45deg, #e74c3c, #3498db, #9b59b6, #e91e63, #ff9800, #4caf50);
  background-size: 600% 600%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: introSlideIn 0.8s ease-out forwards, developerGlow 5s ease-in-out infinite, gentleFloat 6s ease-in-out infinite;
  animation-delay: 3s, 3.3s, 3.5s;
  text-shadow: 0 0 50px rgba(231, 76, 60, 0.7);
  filter: drop-shadow(0 8px 25px rgba(0,0,0,0.4));
}

.intro-version {
  font-size: 3rem;
  font-weight: 800;
  background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: introSlideIn 0.8s ease-out forwards, textGlow 3s ease-in-out infinite;
  animation-delay: 3.6s, 3.9s;
  text-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
}

/* أنيميشن الانترو */
@keyframes introBackground {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes introSlideIn {
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes textGlow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes managerGlow {
  0% {
    background-position: 0% 50%;
    transform: translateY(0px) scale(1);
  }
  25% {
    background-position: 100% 50%;
    transform: translateY(-3px) scale(1.02);
  }
  50% {
    background-position: 200% 50%;
    transform: translateY(-5px) scale(1.05);
  }
  75% {
    background-position: 300% 50%;
    transform: translateY(-3px) scale(1.02);
  }
  100% {
    background-position: 0% 50%;
    transform: translateY(0px) scale(1);
  }
}

@keyframes developerGlow {
  0% {
    background-position: 0% 50%;
    transform: translateY(0px) scale(1) rotateY(0deg);
  }
  20% {
    background-position: 120% 50%;
    transform: translateY(-2px) scale(1.03) rotateY(2deg);
  }
  40% {
    background-position: 240% 50%;
    transform: translateY(-4px) scale(1.06) rotateY(-2deg);
  }
  60% {
    background-position: 360% 50%;
    transform: translateY(-6px) scale(1.08) rotateY(3deg);
  }
  80% {
    background-position: 480% 50%;
    transform: translateY(-4px) scale(1.06) rotateY(-1deg);
  }
  100% {
    background-position: 0% 50%;
    transform: translateY(0px) scale(1) rotateY(0deg);
  }
}

@keyframes gentleFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* خيار الجنس */
.gender-circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background: white;
}

.gender-circle.selected {
  border-color: #007bff;
  background: #007bff;
  color: white;
  transform: scale(1.1);
  box-shadow: 0 0 10px rgba(0, 123, 255, 0.3);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Card Styles */
.card {
  background: white;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* الواجهة الترحيبية */
.welcome-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 15px 35px rgba(0,0,0,0.1);
  text-align: center;
  border: 2px solid rgba(255,255,255,0.3);
}

.welcome-title {
  font-size: 2.2rem;
  font-weight: 800;
  color: #2c3e50;
  margin-bottom: 25px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.management-section {
  margin-bottom: 25px;
}

.management-title {
  font-size: 1.3rem;
  color: #34495e;
  margin-bottom: 10px;
  font-weight: 600;
}

.manager-title {
  font-size: 1.5rem;
  color: #2c3e50;
  margin-bottom: 8px;
  font-weight: 700;
}



.version {
  font-size: 1.2rem;
  color: #7f8c8d;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}



/* Dashboard Cards */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 20px;
  margin-top: 30px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.dashboard-card {
  background: linear-gradient(135deg, var(--card-color-1), var(--card-color-2));
  color: white;
  text-align: center;
  padding: 20px 15px;
  border-radius: 15px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  text-decoration: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  min-height: 120px;
}

.dashboard-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.dashboard-card .icon {
  font-size: 3rem;
  margin-bottom: 10px;
}

.dashboard-card h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

/* Color Schemes for Cards */
.card-data {
  --card-color-1: #667eea;
  --card-color-2: #764ba2;
}

.card-reports {
  --card-color-1: #f093fb;
  --card-color-2: #f5576c;
}

.card-archive {
  --card-color-1: #4facfe;
  --card-color-2: #00f2fe;
}

.card-settings {
  --card-color-1: #43e97b;
  --card-color-2: #38f9d7;
}

.card-calendar {
  --card-color-1: #fa709a;
  --card-color-2: #fee140;
}

.card-logout {
  --card-color-1: #ff9a9e;
  --card-color-2: #fecfef;
}

/* Header */
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  padding: 25px;
  border-radius: 25px;
  margin-bottom: 35px;
  text-align: center;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.header h1 {
  color: #333;
  font-size: 2rem;
  margin-bottom: 10px;
  font-weight: 700;
}

.header .subtitle {
  color: #666;
  font-size: 1.1rem;
  font-weight: 500;
}

/* Form Styles */
.form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 25px;
  padding: 35px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  margin-bottom: 120px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Dashboard specific styles (no bottom navigation) */
.dashboard-container .form-container,
.dashboard-container .card,
.dashboard-container .container {
  margin-bottom: 30px;
}

/* Data Entry Page Styles */
.data-entry-container {
  font-family: 'Arial', sans-serif !important;
}

.data-entry-container * {
  font-family: 'Arial', sans-serif !important;
}

.programmer-header {
  background: linear-gradient(135deg, #4472C4 0%, #2c5aa0 100%);
  color: white;
  padding: 12px 20px;
  border-radius: 15px;
  margin-bottom: 20px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(68, 114, 196, 0.3);
}

.programmer-name {
  font-size: 1.1rem;
  font-weight: 700;
  text-shadow: 0 1px 3px rgba(0,0,0,0.3);
  background: linear-gradient(45deg, #ffffff, #f0f8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: inline-block;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 1rem;
  font-family: 'Cairo', sans-serif;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #667eea;
}

.form-group.required label::after {
  content: " *";
  color: #e74c3c;
}

.error-message {
  color: #e74c3c;
  font-size: 0.9rem;
  margin-top: 5px;
}

/* Buttons */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Cairo', sans-serif;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 25px rgba(102, 126, 234, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #56ab2f, #a8e6cf);
  color: white;
  box-shadow: 0 4px 15px rgba(86, 171, 47, 0.3);
}

.btn-success:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 25px rgba(86, 171, 47, 0.4);
}

.btn-danger {
  background: linear-gradient(135deg, #ff6b6b, #feca57);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.btn-danger:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 25px rgba(255, 107, 107, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #74b9ff, #0984e3);
  color: white;
  box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 25px rgba(116, 185, 255, 0.4);
}

.floating-btn {
  position: fixed;
  bottom: 30px;
  left: 30px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  z-index: 1000;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Table Styles */
.table-container {
  background: white;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.table th,
.table td {
  padding: 12px 15px;
  text-align: right;
  border-bottom: 1px solid #e1e5e9;
}

.table th {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-weight: 600;
}

.table tr:hover {
  background-color: #f8f9fa;
}

/* Navigation */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 15px 8px;
  box-shadow: 0 -8px 25px rgba(0, 0, 0, 0.15);
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: 1000;
  height: 100px;
  border-top: 2px solid #e9ecef;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 5px;
  color: #495057;
  text-decoration: none;
  font-size: 0.8rem;
  font-weight: 700;
  transition: all 0.3s ease;
  cursor: pointer;
  padding: 8px 5px;
  border-radius: 10px;
  min-height: 70px;
  flex: 1;
  max-width: 70px;
}

.nav-item.active,
.nav-item:hover {
  color: #667eea;
  background: rgba(102, 126, 234, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 3px 10px rgba(102, 126, 234, 0.2);
}

.nav-item .icon {
  font-size: 1.4rem;
  margin-bottom: 3px;
  display: block;
}

.nav-item span {
  font-size: 0.8rem !important;
  font-weight: 700 !important;
  text-align: center !important;
  line-height: 1.1 !important;
  white-space: nowrap !important;
  display: block !important;
  color: inherit !important;
  margin-top: 2px !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Force navigation text visibility */
.bottom-nav .nav-item span {
  font-size: 0.8rem !important;
  font-weight: 700 !important;
  color: #495057 !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  text-align: center !important;
  white-space: nowrap !important;
  line-height: 1.1 !important;
  margin-top: 2px !important;
}

/* Special styling for data-entry icon */
.nav-item[data-page="data-entry"] .icon {
  filter: hue-rotate(200deg) saturate(1.5) brightness(1.1);
}

.dashboard-card .icon {
  filter: brightness(1.2) contrast(1.1);
}

.bottom-nav .nav-item.active span,
.bottom-nav .nav-item:hover span {
  color: #667eea !important;
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
  }

  .header h1 {
    font-size: 1.5rem;
  }

  .floating-btn {
    bottom: 120px;
  }

  .nav-item span {
    font-size: 0.75rem !important;
  }
}

/* Loading Animation */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal {
  background: white;
  border-radius: 20px;
  padding: 30px;
  max-width: 500px;
  width: 90%;
  text-align: center;
}

.modal h3 {
  margin-bottom: 20px;
  color: #333;
}

.modal-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 20px;
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  overflow: hidden;
}

.loading-content {
  text-align: center;
  position: relative;
  z-index: 2;
}

.loading-step {
  opacity: 0;
  transform: translateY(30px);
  animation-fill-mode: forwards;
}

.step-1 {
  animation: fadeInUp 1s ease-out 0.5s forwards;
}

.step-2 {
  animation: fadeInUp 1.5s ease-out 1.5s forwards;
}

.step-3 {
  animation: fadeInUp 1s ease-out 4.5s forwards;
}

.step-4 {
  animation: fadeInUp 1s ease-out 6s forwards;
}

.loading-title {
  font-size: 2rem;
  color: white;
  margin: 0;
  font-weight: 600;
  text-shadow: 0 2px 10px rgba(0,0,0,0.3);
}



.copyright {
  font-size: 1.2rem;
  color: rgba(255,255,255,0.9);
  margin: 15px 0;
  font-weight: 500;
}

.app-title {
  font-size: 1.8rem;
  color: white;
  margin: 20px 0 0 0;
  font-weight: 700;
  text-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

/* Developer name in main page */
.developer-name-main {
  background: linear-gradient(45deg, #00ff87, #60efff, #ff6b6b);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 4s ease-in-out infinite;
  font-weight: 900;
  text-shadow: 0 0 20px rgba(255,255,255,0.3);
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
  display: inline-block;
  transform-style: preserve-3d;
}

/* Sparkle Animation */
.loading-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.sparkle {
  position: absolute;
  font-size: 2rem;
  animation: sparkleFloat 4s ease-in-out infinite;
}

.sparkle-1 {
  top: 20%;
  left: 15%;
  animation-delay: 0s;
}

.sparkle-2 {
  top: 30%;
  right: 20%;
  animation-delay: 1s;
}

.sparkle-3 {
  bottom: 25%;
  left: 25%;
  animation-delay: 2s;
}

.sparkle-4 {
  bottom: 35%;
  right: 15%;
  animation-delay: 3s;
}

/* Animations */
@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes glow {
  from {
    text-shadow: 0 0 20px rgba(255,255,255,0.5), 0 0 30px rgba(255,255,255,0.3);
  }
  to {
    text-shadow: 0 0 30px rgba(255,255,255,0.8), 0 0 40px rgba(255,255,255,0.5);
  }
}

@keyframes bounce3d {
  0%, 100% {
    transform: translateY(0) rotateX(0deg);
  }
  50% {
    transform: translateY(-10px) rotateX(5deg);
  }
}

@keyframes sparkleFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg) scale(1);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-20px) rotate(90deg) scale(1.2);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) rotate(180deg) scale(0.8);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-30px) rotate(270deg) scale(1.1);
    opacity: 0.9;
  }
}

/* Filter Page Pulse Animation */
@keyframes pulse {
  0% { box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4); }
  100% { box-shadow: 0 20px 45px rgba(102, 126, 234, 0.6); }
}

/* Gradient Shift Animation for Filter Card */
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Edit Button Pulse Animation */
@keyframes editButtonPulse {
  0% {
    background: linear-gradient(135deg, #ffc107, #ff8c00, #ff6b6b);
    transform: scale(1);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
  }
  50% {
    background: linear-gradient(135deg, #ff6b6b, #ffc107, #ff8c00);
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
  }
  100% {
    background: linear-gradient(135deg, #ffc107, #ff8c00, #ff6b6b);
    transform: scale(1);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
  }
}
