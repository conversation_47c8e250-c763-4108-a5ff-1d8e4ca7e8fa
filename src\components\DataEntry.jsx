import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { motion } from 'framer-motion';
import { FaPlus, FaArrowRight, FaUser, FaPhone, FaIdCard } from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import { useNavigate } from 'react-router-dom';

// Validation schema
const schema = yup.object({
  fullName: yup.string()
    .required('الاسم الكامل مطلوب')
    .min(2, 'الاسم يجب أن يكون أكثر من حرفين'),
  motherName: yup.string()
    .required('اسم الأم الكامل مطلوب')
    .min(2, 'اسم الأم يجب أن يكون أكثر من حرفين'),
  birthDate: yup.date()
    .required('تاريخ الميلاد مطلوب')
    .max(new Date(), 'تاريخ الميلاد لا يمكن أن يكون في المستقبل'),
  nationalId: yup.string()
    .required('الرقم الوطني مطلوب')
    .matches(/^\d{12}$/, 'الرقم الوطني يجب أن يكون 12 رقم'),
  phoneNumber: yup.string()
    .required('رقم الهاتف مطلوب')
    .matches(/^07\d{9}$/, 'رقم الهاتف يجب أن يبدأ بـ 07 ويكون 11 رقم'),
  transferOrderNumber: yup.string()
    .required('رقم أمر النقل مطلوب'),
  orderStartDate: yup.date()
    .required('تاريخ بداية الأمر مطلوب'),
  recycledCircle: yup.string()
    .required('الدائرة المعاد تدويرها مطلوبة'),
  specialization: yup.string()
    .required('التخصص مطلوب')
});

const DataEntry = () => {
  const { addEmployee, setLoading, setError } = useApp();
  const navigate = useNavigate();
  const [showSuccess, setShowSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm({
    resolver: yupResolver(schema)
  });

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      addEmployee(data);
      setShowSuccess(true);
      reset();
      
      setTimeout(() => {
        setShowSuccess(false);
      }, 3000);
      
    } catch (error) {
      setError('حدث خطأ أثناء حفظ البيانات');
    } finally {
      setLoading(false);
    }
  };

  const formVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const fieldVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.4 }
    }
  };

  return (
    <div className="container">
      {/* Header */}
      <motion.div 
        className="header"
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <h1>
          <FaUser style={{ marginLeft: '10px' }} />
          إدخال بيانات الموظفين
        </h1>
        <div className="subtitle">
          املأ جميع الحقول المطلوبة لإضافة موظف جديد
        </div>
      </motion.div>

      {/* Success Message */}
      {showSuccess && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          className="card"
          style={{
            background: 'linear-gradient(135deg, #43e97b, #38f9d7)',
            color: 'white',
            textAlign: 'center',
            padding: '20px',
            marginBottom: '20px'
          }}
        >
          <h3>✅ تم حفظ البيانات بنجاح!</h3>
          <p>تم إضافة الموظف الجديد إلى النظام</p>
        </motion.div>
      )}

      {/* Form */}
      <motion.form
        className="form-container"
        onSubmit={handleSubmit(onSubmit)}
        variants={formVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Auto-generated ID */}
        <motion.div className="form-group" variants={fieldVariants}>
          <label>
            <FaIdCard style={{ marginLeft: '5px' }} />
            معرف الموظف (تلقائي)
          </label>
          <input
            type="text"
            value={`EMP-${Date.now().toString().slice(-6)}`}
            disabled
            style={{ backgroundColor: '#f8f9fa', cursor: 'not-allowed' }}
          />
        </motion.div>

        {/* Full Name */}
        <motion.div className="form-group required" variants={fieldVariants}>
          <label>الاسم الكامل</label>
          <input
            type="text"
            {...register('fullName')}
            placeholder="أدخل الاسم الكامل"
          />
          {errors.fullName && (
            <div className="error-message">{errors.fullName.message}</div>
          )}
        </motion.div>

        {/* Mother's Name */}
        <motion.div className="form-group required" variants={fieldVariants}>
          <label>اسم الأم الكامل</label>
          <input
            type="text"
            {...register('motherName')}
            placeholder="أدخل اسم الأم الكامل"
          />
          {errors.motherName && (
            <div className="error-message">{errors.motherName.message}</div>
          )}
        </motion.div>

        {/* Birth Date */}
        <motion.div className="form-group required" variants={fieldVariants}>
          <label>تاريخ الميلاد</label>
          <input
            type="date"
            {...register('birthDate')}
          />
          {errors.birthDate && (
            <div className="error-message">{errors.birthDate.message}</div>
          )}
        </motion.div>

        {/* National ID */}
        <motion.div className="form-group required" variants={fieldVariants}>
          <label>الرقم الوطني الموحد</label>
          <input
            type="text"
            {...register('nationalId')}
            placeholder="أدخل الرقم الوطني (12 رقم)"
            maxLength="12"
          />
          {errors.nationalId && (
            <div className="error-message">{errors.nationalId.message}</div>
          )}
        </motion.div>

        {/* Phone Number */}
        <motion.div className="form-group required" variants={fieldVariants}>
          <label>
            <FaPhone style={{ marginLeft: '5px' }} />
            رقم الهاتف
          </label>
          <input
            type="tel"
            {...register('phoneNumber')}
            placeholder="07xxxxxxxxx"
            maxLength="11"
          />
          {errors.phoneNumber && (
            <div className="error-message">{errors.phoneNumber.message}</div>
          )}
        </motion.div>

        {/* Transfer Order Number */}
        <motion.div className="form-group required" variants={fieldVariants}>
          <label>رقم أمر النقل</label>
          <input
            type="text"
            {...register('transferOrderNumber')}
            placeholder="أدخل رقم أمر النقل"
          />
          {errors.transferOrderNumber && (
            <div className="error-message">{errors.transferOrderNumber.message}</div>
          )}
        </motion.div>

        {/* Order Start Date */}
        <motion.div className="form-group required" variants={fieldVariants}>
          <label>تاريخ بداية الأمر</label>
          <input
            type="date"
            {...register('orderStartDate')}
          />
          {errors.orderStartDate && (
            <div className="error-message">{errors.orderStartDate.message}</div>
          )}
        </motion.div>

        {/* Recycled Circle */}
        <motion.div className="form-group required" variants={fieldVariants}>
          <label>الدائرة المعاد تدويرها</label>
          <textarea
            {...register('recycledCircle')}
            placeholder="أدخل تفاصيل الدائرة المعاد تدويرها"
            rows="3"
          />
          {errors.recycledCircle && (
            <div className="error-message">{errors.recycledCircle.message}</div>
          )}
        </motion.div>

        {/* Specialization */}
        <motion.div className="form-group required" variants={fieldVariants}>
          <label>التخصص</label>
          <select {...register('specialization')}>
            <option value="">اختر التخصص</option>
            <option value="محاسبة">محاسبة</option>
            <option value="إدارة">إدارة</option>
            <option value="هندسة">هندسة</option>
            <option value="طب">طب</option>
            <option value="تعليم">تعليم</option>
            <option value="قانون">قانون</option>
            <option value="تقنية معلومات">تقنية معلومات</option>
            <option value="أخرى">أخرى</option>
          </select>
          {errors.specialization && (
            <div className="error-message">{errors.specialization.message}</div>
          )}
        </motion.div>

        {/* Submit Buttons */}
        <motion.div 
          style={{ 
            display: 'flex', 
            gap: '15px', 
            justifyContent: 'center',
            marginTop: '30px'
          }}
          variants={fieldVariants}
        >
          <button type="submit" className="btn btn-primary">
            <FaPlus style={{ marginLeft: '5px' }} />
            إضافة موظف
          </button>
          
          <button 
            type="button" 
            className="btn btn-secondary"
            onClick={() => navigate('/')}
          >
            <FaArrowRight style={{ marginLeft: '5px' }} />
            العودة للرئيسية
          </button>
        </motion.div>
      </motion.form>
    </div>
  );
};

export default DataEntry;
