import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  FaArchive, 
  FaTrash, 
  FaPrint, 
  FaFileExcel,
  FaEye,
  FaEdit,
  FaArrowRight,
  FaLock,
  FaUsers
} from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import { useNavigate } from 'react-router-dom';
import * as XLSX from 'xlsx';

const Archive = () => {
  const { employees, deleteEmployee, currentUser } = useApp();
  const navigate = useNavigate();
  const [selectedEmployees, setSelectedEmployees] = useState([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState(null);

  // Check if user is admin
  const isAdmin = currentUser.role === 'admin';

  if (!isAdmin) {
    return (
      <div className="container">
        <motion.div 
          className="header"
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.6 }}
        >
          <h1>
            <FaLock style={{ marginLeft: '10px' }} />
            الأرشيف العام
          </h1>
          <div className="subtitle">
            عذراً، هذه الصفحة مخصصة للمدير فقط
          </div>
        </motion.div>

        <motion.div 
          className="card"
          style={{ textAlign: 'center', padding: '40px' }}
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.3 }}
        >
          <FaLock style={{ fontSize: '4rem', color: '#e74c3c', marginBottom: '20px' }} />
          <h3 style={{ color: '#333', marginBottom: '15px' }}>وصول محظور</h3>
          <p style={{ color: '#666', marginBottom: '20px' }}>
            تحتاج إلى صلاحيات المدير للوصول إلى الأرشيف العام
          </p>
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/')}
          >
            <FaArrowRight style={{ marginLeft: '5px' }} />
            العودة للرئيسية
          </button>
        </motion.div>
      </div>
    );
  }

  const handleSelectEmployee = (employeeId) => {
    setSelectedEmployees(prev => 
      prev.includes(employeeId) 
        ? prev.filter(id => id !== employeeId)
        : [...prev, employeeId]
    );
  };

  const handleSelectAll = () => {
    if (selectedEmployees.length === employees.length) {
      setSelectedEmployees([]);
    } else {
      setSelectedEmployees(employees.map(emp => emp.id));
    }
  };

  const handleDeleteSelected = () => {
    selectedEmployees.forEach(id => deleteEmployee(id));
    setSelectedEmployees([]);
    setShowDeleteModal(false);
  };

  const exportFullReport = () => {
    const exportData = employees.map((emp, index) => ({
      'الرقم': index + 1,
      'الاسم الكامل': emp.fullName,
      'اسم الأم': emp.motherName,
      'تاريخ الميلاد': new Date(emp.birthDate).toLocaleDateString('ar-SA'),
      'الرقم الوطني': emp.nationalId,
      'رقم الهاتف': emp.phoneNumber,
      'رقم أمر النقل': emp.transferOrderNumber,
      'تاريخ بداية الأمر': new Date(emp.orderStartDate).toLocaleDateString('ar-SA'),
      'الدائرة المعاد تدويرها': emp.recycledCircle,
      'التخصص': emp.specialization,
      'تاريخ الإدخال': new Date(emp.createdAt).toLocaleDateString('ar-SA'),
      'حالة التنفيذ': 'مكتمل'
    }));

    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'الأرشيف العام');
    
    XLSX.writeFile(wb, `الأرشيف_العام_${new Date().toLocaleDateString('ar-SA')}.xlsx`);
  };

  const printFullReport = () => {
    const printWindow = window.open('', '_blank');
    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <title>تقرير الأرشيف العام</title>
        <style>
          body { font-family: Arial, sans-serif; direction: rtl; }
          table { width: 100%; border-collapse: collapse; margin-top: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
          th { background-color: #f2f2f2; font-weight: bold; }
          .header { text-align: center; margin-bottom: 30px; }
          .header h1 { color: #333; }
          .header p { color: #666; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>تقرير الأرشيف العام</h1>
          <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
          <p>إجمالي الموظفين: ${employees.length}</p>
        </div>
        <table>
          <thead>
            <tr>
              <th>الرقم</th>
              <th>الاسم الكامل</th>
              <th>تاريخ الميلاد</th>
              <th>الرقم الوطني</th>
              <th>رقم الهاتف</th>
              <th>التخصص</th>
              <th>حالة التنفيذ</th>
            </tr>
          </thead>
          <tbody>
            ${employees.map((emp, index) => `
              <tr>
                <td>${index + 1}</td>
                <td>${emp.fullName}</td>
                <td>${new Date(emp.birthDate).toLocaleDateString('ar-SA')}</td>
                <td>${emp.nationalId}</td>
                <td>${emp.phoneNumber}</td>
                <td>${emp.specialization}</td>
                <td>مكتمل</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </body>
      </html>
    `;
    
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
  };

  return (
    <div className="container">
      {/* Header */}
      <motion.div 
        className="header"
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <h1>
          <FaArchive style={{ marginLeft: '10px' }} />
          الأرشيف العام
        </h1>
        <div className="subtitle">
          إدارة وأرشفة بيانات الموظفين - صلاحيات المدير
        </div>
      </motion.div>

      {/* Action Buttons */}
      <motion.div 
        style={{ 
          display: 'flex', 
          gap: '15px', 
          marginBottom: '30px',
          flexWrap: 'wrap',
          justifyContent: 'center'
        }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <button 
          className="btn btn-primary"
          onClick={printFullReport}
        >
          <FaPrint style={{ marginLeft: '5px' }} />
          طباعة التقرير الكامل
        </button>

        <button 
          className="btn btn-success"
          onClick={exportFullReport}
        >
          <FaFileExcel style={{ marginLeft: '5px' }} />
          تصدير Excel
        </button>

        <button 
          className="btn btn-danger"
          onClick={() => setShowDeleteModal(true)}
          disabled={selectedEmployees.length === 0}
        >
          <FaTrash style={{ marginLeft: '5px' }} />
          حذف المحدد ({selectedEmployees.length})
        </button>

        <button 
          className="btn btn-secondary"
          onClick={() => navigate('/')}
        >
          <FaArrowRight style={{ marginLeft: '5px' }} />
          العودة للرئيسية
        </button>
      </motion.div>

      {/* Statistics */}
      <motion.div 
        className="dashboard-grid"
        style={{ marginBottom: '30px' }}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 }}
      >
        <div className="card" style={{ textAlign: 'center' }}>
          <FaUsers style={{ fontSize: '2rem', color: '#667eea', marginBottom: '10px' }} />
          <h3>إجمالي الموظفين</h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#667eea' }}>
            {employees.length}
          </div>
        </div>

        <div className="card" style={{ textAlign: 'center' }}>
          <FaArchive style={{ fontSize: '2rem', color: '#38f9d7', marginBottom: '10px' }} />
          <h3>المحدد للحذف</h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#e74c3c' }}>
            {selectedEmployees.length}
          </div>
        </div>

        <div className="card" style={{ textAlign: 'center' }}>
          <FaFileExcel style={{ fontSize: '2rem', color: '#43e97b', marginBottom: '10px' }} />
          <h3>جاهز للتصدير</h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#43e97b' }}>
            {employees.length}
          </div>
        </div>
      </motion.div>

      {/* Data Table */}
      <motion.div 
        className="table-container"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.5 }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <h3 style={{ color: '#333' }}>
            بيانات الموظفين ({employees.length})
          </h3>
          <label style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
            <input
              type="checkbox"
              checked={selectedEmployees.length === employees.length && employees.length > 0}
              onChange={handleSelectAll}
            />
            تحديد الكل
          </label>
        </div>
        
        {employees.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '40px', color: '#666' }}>
            <FaUsers style={{ fontSize: '3rem', marginBottom: '15px', opacity: 0.3 }} />
            <p>لا توجد بيانات في الأرشيف</p>
          </div>
        ) : (
          <div style={{ overflowX: 'auto' }}>
            <table className="table">
              <thead>
                <tr>
                  <th>تحديد</th>
                  <th>الرقم</th>
                  <th>الاسم الكامل</th>
                  <th>الرقم الوطني</th>
                  <th>رقم الهاتف</th>
                  <th>التخصص</th>
                  <th>تاريخ الإدخال</th>
                  <th>حالة التنفيذ</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {employees.map((employee, index) => (
                  <motion.tr 
                    key={employee.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    style={{
                      backgroundColor: selectedEmployees.includes(employee.id) ? '#f0f8ff' : 'transparent'
                    }}
                  >
                    <td>
                      <input
                        type="checkbox"
                        checked={selectedEmployees.includes(employee.id)}
                        onChange={() => handleSelectEmployee(employee.id)}
                      />
                    </td>
                    <td>{index + 1}</td>
                    <td>{employee.fullName}</td>
                    <td>{employee.nationalId}</td>
                    <td>{employee.phoneNumber}</td>
                    <td>{employee.specialization}</td>
                    <td>{new Date(employee.createdAt).toLocaleDateString('ar-SA')}</td>
                    <td>
                      <span style={{ 
                        background: 'linear-gradient(135deg, #43e97b, #38f9d7)',
                        color: 'white',
                        padding: '4px 8px',
                        borderRadius: '12px',
                        fontSize: '0.8rem'
                      }}>
                        مكتمل
                      </span>
                    </td>
                    <td>
                      <div style={{ display: 'flex', gap: '5px' }}>
                        <button 
                          className="btn btn-secondary"
                          style={{ padding: '5px 10px', fontSize: '0.8rem' }}
                          onClick={() => setEditingEmployee(employee)}
                        >
                          <FaEye />
                        </button>
                        <button 
                          className="btn btn-danger"
                          style={{ padding: '5px 10px', fontSize: '0.8rem' }}
                          onClick={() => {
                            if (window.confirm('هل أنت متأكد من حذف هذا الموظف؟')) {
                              deleteEmployee(employee.id);
                            }
                          }}
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </motion.div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="modal-overlay">
          <motion.div 
            className="modal"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
          >
            <h3>تأكيد الحذف</h3>
            <p>هل أنت متأكد من حذف {selectedEmployees.length} موظف؟</p>
            <p style={{ color: '#e74c3c', fontSize: '0.9rem' }}>
              هذا الإجراء لا يمكن التراجع عنه
            </p>
            <div className="modal-buttons">
              <button 
                className="btn btn-danger"
                onClick={handleDeleteSelected}
              >
                تأكيد الحذف
              </button>
              <button 
                className="btn btn-secondary"
                onClick={() => setShowDeleteModal(false)}
              >
                إلغاء
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default Archive;
