import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  FaHome, 
  FaUser, 
  FaChartBar, 
  FaCog,
  FaCalendarAlt
} from 'react-icons/fa';

const BottomNavigation = () => {
  const location = useLocation();

  const navItems = [
    {
      path: '/',
      icon: <FaHome />,
      label: 'الرئيسية',
      color: '#667eea'
    },
    {
      path: '/data-entry',
      icon: <FaUser />,
      label: 'البيانات',
      color: '#f5576c'
    },
    {
      path: '/reports',
      icon: <FaChartBar />,
      label: 'التقارير',
      color: '#38f9d7'
    },
    {
      path: '/calendar',
      icon: <FaCalendarAlt />,
      label: 'التقويم',
      color: '#43e97b'
    },
    {
      path: '/settings',
      icon: <FaCog />,
      label: 'الإعدادات',
      color: '#fa709a'
    }
  ];

  return (
    <motion.nav 
      className="bottom-nav"
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
    >
      {navItems.map((item, index) => {
        const isActive = location.pathname === item.path;
        
        return (
          <Link
            key={index}
            to={item.path}
            className={`nav-item ${isActive ? 'active' : ''}`}
            style={{
              color: isActive ? item.color : '#666'
            }}
          >
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '5px'
              }}
            >
              <div 
                className="icon"
                style={{
                  fontSize: '1.5rem',
                  transition: 'all 0.3s ease'
                }}
              >
                {item.icon}
              </div>
              <span style={{ fontSize: '0.8rem', fontWeight: isActive ? '600' : '400' }}>
                {item.label}
              </span>
              
              {isActive && (
                <motion.div
                  layoutId="activeIndicator"
                  style={{
                    width: '4px',
                    height: '4px',
                    borderRadius: '50%',
                    backgroundColor: item.color,
                    position: 'absolute',
                    bottom: '5px'
                  }}
                  initial={false}
                  transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                />
              )}
            </motion.div>
          </Link>
        );
      })}
    </motion.nav>
  );
};

export default BottomNavigation;
