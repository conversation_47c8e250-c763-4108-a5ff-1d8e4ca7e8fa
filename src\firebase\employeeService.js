// Employee Database Service
import { 
  collection, 
  addDoc, 
  getDocs, 
  doc, 
  updateDoc, 
  deleteDoc,
  query,
  orderBy,
  onSnapshot
} from 'firebase/firestore';
import { db } from './config.js';

const COLLECTION_NAME = 'employees';

// إضافة موظف جديد
export const addEmployee = async (employeeData) => {
  try {
    const docRef = await addDoc(collection(db, COLLECTION_NAME), {
      ...employeeData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
    
    console.log('تم إضافة الموظف بنجاح:', docRef.id);
    return { success: true, id: docRef.id };
  } catch (error) {
    console.error('خطأ في إضافة الموظف:', error);
    return { success: false, error: error.message };
  }
};

// جلب جميع الموظفين
export const getAllEmployees = async () => {
  try {
    const q = query(collection(db, COLLECTION_NAME), orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);
    
    const employees = [];
    querySnapshot.forEach((doc) => {
      employees.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    return { success: true, data: employees };
  } catch (error) {
    console.error('خطأ في جلب الموظفين:', error);
    return { success: false, error: error.message };
  }
};

// الاستماع للتغييرات في الوقت الفعلي
export const subscribeToEmployees = (callback) => {
  const q = query(collection(db, COLLECTION_NAME), orderBy('createdAt', 'desc'));
  
  return onSnapshot(q, (querySnapshot) => {
    const employees = [];
    querySnapshot.forEach((doc) => {
      employees.push({
        id: doc.id,
        ...doc.data()
      });
    });
    callback(employees);
  }, (error) => {
    console.error('خطأ في الاستماع للتغييرات:', error);
  });
};

// تحديث موظف
export const updateEmployee = async (id, updateData) => {
  try {
    const employeeRef = doc(db, COLLECTION_NAME, id);
    await updateDoc(employeeRef, {
      ...updateData,
      updatedAt: new Date().toISOString()
    });
    
    console.log('تم تحديث الموظف بنجاح');
    return { success: true };
  } catch (error) {
    console.error('خطأ في تحديث الموظف:', error);
    return { success: false, error: error.message };
  }
};

// حذف موظف
export const deleteEmployee = async (id) => {
  try {
    await deleteDoc(doc(db, COLLECTION_NAME, id));
    console.log('تم حذف الموظف بنجاح');
    return { success: true };
  } catch (error) {
    console.error('خطأ في حذف الموظف:', error);
    return { success: false, error: error.message };
  }
};

// فحص الاتصال بقاعدة البيانات
export const testConnection = async () => {
  try {
    const testCollection = collection(db, 'test');
    await getDocs(testCollection);
    return { success: true, message: 'الاتصال بقاعدة البيانات ناجح' };
  } catch (error) {
    console.error('خطأ في الاتصال:', error);
    return { success: false, error: error.message };
  }
};
