# 🚀 دليل النشر السريع

## خطوات النشر في 5 دقائق

### 1️⃣ تسجيل الدخول إلى Firebase
```bash
npm run firebase:login
```

### 2️⃣ إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. اضغط "Add project"
3. اسم المشروع: `alqaisar-contract-system`
4. اتبع الخطوات

### 3️⃣ ربط المشروع
```bash
npm run firebase:init
```
**اختر:**
- ✅ Hosting
- ✅ Use existing project: `alqaisar-contract-system`
- ✅ Public directory: `dist`
- ✅ Single-page app: `Yes`

### 4️⃣ النشر
```bash
npm run deploy
```

### 5️⃣ تم! 🎉
ستحصل على رابط مثل:
`https://alqaisar-contract-system.web.app`

---

## أوامر مفيدة

| الأمر | الوصف |
|-------|--------|
| `npm run check` | فحص جاهزية المشروع |
| `npm run build` | بناء المشروع |
| `npm run deploy` | نشر على Firebase |
| `firebase open hosting:site` | فتح الموقع |

---

## حل المشاكل السريع

**مشكلة:** Firebase command not found
```bash
npm install -g firebase-tools
```

**مشكلة:** Permission denied
```bash
firebase logout
firebase login
```

**مشكلة:** Build failed
```bash
npm run build
```

---

**💡 نصيحة:** استخدم `npm run check` قبل النشر للتأكد من جاهزية المشروع
