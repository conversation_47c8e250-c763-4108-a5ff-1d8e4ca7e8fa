import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  FaDatabase, 
  FaChartBar, 
  FaArchive, 
  FaCog, 
  FaCalendarAlt, 
  FaSignOutAlt,
  FaPlus
} from 'react-icons/fa';
import { useApp } from '../context/AppContext';

const Dashboard = () => {
  const { currentUser, employees } = useApp();

  const dashboardCards = [
    {
      title: 'البيانات',
      icon: <FaDatabase />,
      path: '/data-entry',
      className: 'card-data',
      description: 'إدخال بيانات الموظفين'
    },
    {
      title: 'التقارير',
      icon: <FaChartBar />,
      path: '/reports',
      className: 'card-reports',
      description: 'عرض وتصدير التقارير'
    },
    {
      title: 'الأرشيف العام',
      icon: <FaArchive />,
      path: '/archive',
      className: 'card-archive',
      description: 'إدارة الأرشيف'
    },
    {
      title: 'التقويم',
      icon: <FaCalendarAlt />,
      path: '/calendar',
      className: 'card-calendar',
      description: 'المواعيد والجدولة'
    },
    {
      title: 'الإعدادات',
      icon: <FaCog />,
      path: '/settings',
      className: 'card-settings',
      description: 'إعدادات النظام'
    },
    {
      title: 'تسجيل الخروج',
      icon: <FaSignOutAlt />,
      path: '/logout',
      className: 'card-logout',
      description: 'خروج من النظام'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <div className="container">
      {/* Header */}
      <motion.div 
        className="header"
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <h1>واجهة إدخال بيانات العقود (5902)</h1>
        <div className="subtitle">
          <strong>مبرمج المحاسبة:</strong> {currentUser.name}
          <br />
          {currentUser.governorate}
        </div>
        <div style={{ marginTop: '15px', color: '#667eea', fontWeight: '600' }}>
          إجمالي الموظفين المسجلين: {employees.length}
        </div>
      </motion.div>

      {/* Dashboard Cards */}
      <motion.div 
        className="dashboard-grid"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {dashboardCards.map((card, index) => (
          <motion.div
            key={index}
            variants={cardVariants}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {card.path === '/logout' ? (
              <div 
                className={`dashboard-card ${card.className}`}
                onClick={() => {
                  if (window.confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                    // Handle logout logic here
                    alert('تم تسجيل الخروج بنجاح');
                  }
                }}
              >
                <div className="icon">{card.icon}</div>
                <h3>{card.title}</h3>
                <p style={{ fontSize: '0.9rem', opacity: 0.9 }}>
                  {card.description}
                </p>
              </div>
            ) : (
              <Link to={card.path} className={`dashboard-card ${card.className}`}>
                <div className="icon">{card.icon}</div>
                <h3>{card.title}</h3>
                <p style={{ fontSize: '0.9rem', opacity: 0.9 }}>
                  {card.description}
                </p>
              </Link>
            )}
          </motion.div>
        ))}
      </motion.div>

      {/* Floating Add Button */}
      <Link to="/data-entry" className="floating-btn btn-primary">
        <FaPlus />
      </Link>

      {/* Statistics Cards */}
      <motion.div 
        className="dashboard-grid"
        style={{ marginTop: '30px' }}
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8, duration: 0.6 }}
      >
        <div className="card" style={{ textAlign: 'center', padding: '20px' }}>
          <h3 style={{ color: '#667eea', marginBottom: '10px' }}>إحصائيات اليوم</h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#333' }}>
            {employees.filter(emp => {
              const today = new Date().toDateString();
              const empDate = new Date(emp.createdAt).toDateString();
              return today === empDate;
            }).length}
          </div>
          <p style={{ color: '#666' }}>موظف جديد</p>
        </div>

        <div className="card" style={{ textAlign: 'center', padding: '20px' }}>
          <h3 style={{ color: '#f5576c', marginBottom: '10px' }}>هذا الأسبوع</h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#333' }}>
            {employees.filter(emp => {
              const weekAgo = new Date();
              weekAgo.setDate(weekAgo.getDate() - 7);
              return new Date(emp.createdAt) >= weekAgo;
            }).length}
          </div>
          <p style={{ color: '#666' }}>موظف جديد</p>
        </div>

        <div className="card" style={{ textAlign: 'center', padding: '20px' }}>
          <h3 style={{ color: '#38f9d7', marginBottom: '10px' }}>إجمالي العقود</h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#333' }}>
            {employees.length}
          </div>
          <p style={{ color: '#666' }}>عقد مسجل</p>
        </div>
      </motion.div>
    </div>
  );
};

export default Dashboard;
