import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  FaCalendarAlt, 
  FaPlus, 
  FaArrowRight,
  FaUsers,
  FaClock,
  FaCalendarDay,
  FaChevronLeft,
  FaChevronRight
} from 'react-icons/fa';
import { useApp } from '../context/AppContext';
import { useNavigate } from 'react-router-dom';

const Calendar = () => {
  const { employees } = useApp();
  const navigate = useNavigate();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(null);

  // Get entries for a specific date
  const getEntriesForDate = (date) => {
    return employees.filter(emp => {
      const empDate = new Date(emp.createdAt);
      return empDate.toDateString() === date.toDateString();
    });
  };

  // Generate calendar days
  const generateCalendarDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days = [];
    const currentDateObj = new Date(startDate);

    for (let i = 0; i < 42; i++) {
      const dayEntries = getEntriesForDate(currentDateObj);
      days.push({
        date: new Date(currentDateObj),
        isCurrentMonth: currentDateObj.getMonth() === month,
        isToday: currentDateObj.toDateString() === new Date().toDateString(),
        entriesCount: dayEntries.length,
        entries: dayEntries
      });
      currentDateObj.setDate(currentDateObj.getDate() + 1);
    }

    return days;
  };

  const calendarDays = generateCalendarDays();

  const navigateMonth = (direction) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(newDate.getMonth() + direction);
    setCurrentDate(newDate);
  };

  const monthNames = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];

  const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.3 }
    }
  };

  return (
    <div className="container">
      {/* Header */}
      <motion.div 
        className="header"
        initial={{ y: -50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <h1>
          <FaCalendarAlt style={{ marginLeft: '10px' }} />
          التقويم والجدولة
        </h1>
        <div className="subtitle">
          عرض الإدخالات اليومية والمواعيد المجدولة
        </div>
      </motion.div>

      {/* Statistics */}
      <motion.div 
        className="dashboard-grid"
        style={{ marginBottom: '30px' }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <div className="card" style={{ textAlign: 'center' }}>
          <FaUsers style={{ fontSize: '2rem', color: '#667eea', marginBottom: '10px' }} />
          <h3>إدخالات اليوم</h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#667eea' }}>
            {getEntriesForDate(new Date()).length}
          </div>
        </div>

        <div className="card" style={{ textAlign: 'center' }}>
          <FaCalendarDay style={{ fontSize: '2rem', color: '#f5576c', marginBottom: '10px' }} />
          <h3>هذا الشهر</h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#f5576c' }}>
            {employees.filter(emp => {
              const empDate = new Date(emp.createdAt);
              return empDate.getMonth() === currentDate.getMonth() && 
                     empDate.getFullYear() === currentDate.getFullYear();
            }).length}
          </div>
        </div>

        <div className="card" style={{ textAlign: 'center' }}>
          <FaClock style={{ fontSize: '2rem', color: '#38f9d7', marginBottom: '10px' }} />
          <h3>أيام نشطة</h3>
          <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#38f9d7' }}>
            {calendarDays.filter(day => day.entriesCount > 0 && day.isCurrentMonth).length}
          </div>
        </div>
      </motion.div>

      {/* Calendar */}
      <motion.div 
        className="form-container"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.4 }}
      >
        {/* Calendar Header */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: '20px'
        }}>
          <button 
            className="btn btn-secondary"
            onClick={() => navigateMonth(1)}
            style={{ padding: '10px 15px' }}
          >
            <FaChevronLeft />
          </button>
          
          <h3 style={{ color: '#333', margin: 0 }}>
            {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
          </h3>
          
          <button 
            className="btn btn-secondary"
            onClick={() => navigateMonth(-1)}
            style={{ padding: '10px 15px' }}
          >
            <FaChevronRight />
          </button>
        </div>

        {/* Day Names */}
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(7, 1fr)',
          gap: '5px',
          marginBottom: '10px'
        }}>
          {dayNames.map(day => (
            <div key={day} style={{
              textAlign: 'center',
              padding: '10px',
              fontWeight: 'bold',
              color: '#666',
              backgroundColor: '#f8f9fa',
              borderRadius: '8px'
            }}>
              {day}
            </div>
          ))}
        </div>

        {/* Calendar Grid */}
        <motion.div 
          style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(7, 1fr)',
            gap: '5px'
          }}
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {calendarDays.map((day, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              style={{
                minHeight: '60px',
                padding: '8px',
                border: '1px solid #e1e5e9',
                borderRadius: '8px',
                cursor: 'pointer',
                backgroundColor: day.isCurrentMonth ? 'white' : '#f8f9fa',
                opacity: day.isCurrentMonth ? 1 : 0.6,
                position: 'relative',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'space-between',
                ...(day.isToday && {
                  backgroundColor: '#667eea',
                  color: 'white',
                  fontWeight: 'bold'
                }),
                ...(selectedDate && selectedDate.toDateString() === day.date.toDateString() && {
                  backgroundColor: '#f093fb',
                  color: 'white'
                })
              }}
              onClick={() => setSelectedDate(day.date)}
            >
              <div style={{ fontSize: '0.9rem' }}>
                {day.date.getDate()}
              </div>
              
              {day.entriesCount > 0 && (
                <div style={{
                  backgroundColor: day.isToday ? 'rgba(255,255,255,0.3)' : '#43e97b',
                  color: day.isToday ? 'white' : 'white',
                  borderRadius: '12px',
                  padding: '2px 6px',
                  fontSize: '0.7rem',
                  textAlign: 'center',
                  fontWeight: 'bold'
                }}>
                  {day.entriesCount}
                </div>
              )}
            </motion.div>
          ))}
        </motion.div>
      </motion.div>

      {/* Selected Date Details */}
      {selectedDate && (
        <motion.div 
          className="form-container"
          style={{ marginTop: '20px' }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <h3 style={{ marginBottom: '15px', color: '#333' }}>
            تفاصيل يوم {selectedDate.toLocaleDateString('ar-SA')}
          </h3>
          
          {getEntriesForDate(selectedDate).length === 0 ? (
            <div style={{ textAlign: 'center', padding: '20px', color: '#666' }}>
              <FaCalendarDay style={{ fontSize: '2rem', marginBottom: '10px', opacity: 0.3 }} />
              <p>لا توجد إدخالات في هذا اليوم</p>
            </div>
          ) : (
            <div style={{ overflowX: 'auto' }}>
              <table className="table">
                <thead>
                  <tr>
                    <th>الاسم الكامل</th>
                    <th>رقم الهاتف</th>
                    <th>التخصص</th>
                    <th>وقت الإدخال</th>
                  </tr>
                </thead>
                <tbody>
                  {getEntriesForDate(selectedDate).map((employee, index) => (
                    <tr key={employee.id}>
                      <td>{employee.fullName}</td>
                      <td>{employee.phoneNumber}</td>
                      <td>{employee.specialization}</td>
                      <td>{new Date(employee.createdAt).toLocaleTimeString('ar-SA')}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </motion.div>
      )}

      {/* Action Buttons */}
      <motion.div 
        style={{ 
          display: 'flex', 
          gap: '15px', 
          justifyContent: 'center',
          marginTop: '30px'
        }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <button 
          className="btn btn-primary"
          onClick={() => navigate('/data-entry')}
        >
          <FaPlus style={{ marginLeft: '5px' }} />
          إضافة موظف جديد
        </button>
        
        <button 
          className="btn btn-secondary"
          onClick={() => navigate('/')}
        >
          <FaArrowRight style={{ marginLeft: '5px' }} />
          العودة للرئيسية
        </button>
      </motion.div>
    </div>
  );
};

export default Calendar;
